"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  Shield, 
  Users, 
  BookOpen, 
  Heart, 
  Mail,
  Twitter,
  Github,
  Youtube,
  MessageCircle
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ByteHeroFooterProps {
  onSignupClick: () => void
}

export function ByteHeroFooter({ onSignupClick }: ByteHeroFooterProps) {
  const footerSections = [
    {
      title: 'Platform',
      links: [
        { label: 'Tutorial Zone', href: '#' },
        { label: 'Hack Lab', href: '#' },
        { label: 'Creator Studio', href: '#' },
        { label: 'Community Lounge', href: '#' },
        { label: 'Epoch Towns', href: '#' }
      ]
    },
    {
      title: 'Safety & Trust',
      links: [
        { label: 'Privacy Policy', href: '#' },
        { label: 'Terms of Service', href: '#' },
        { label: 'COPPA Compliance', href: '#' },
        { label: 'Community Guidelines', href: '#' },
        { label: 'Report Content', href: '#' }
      ]
    },
    {
      title: 'Support',
      links: [
        { label: 'Help Center', href: '#' },
        { label: 'Parent Guide', href: '#' },
        { label: 'Teacher Resources', href: '#' },
        { label: 'Contact Us', href: '#' },
        { label: 'System Status', href: '#' }
      ]
    },
    {
      title: 'Community',
      links: [
        { label: 'Discord Server', href: '#' },
        { label: 'Student Showcase', href: '#' },
        { label: 'Mentor Program', href: '#' },
        { label: 'Events & Tournaments', href: '#' },
        { label: 'Blog', href: '#' }
      ]
    }
  ]

  const socialLinks = [
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Youtube, href: '#', label: 'YouTube' },
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: MessageCircle, href: '#', label: 'Discord' }
  ]

  return (
    <footer className="bg-black/40 border-t border-gray-800/50 backdrop-blur-xl">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        {/* Top Section - Logo and CTA */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="lg:col-span-1"
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  ByteHero
                </h3>
                <p className="text-sm text-gray-400">Learn • Play • Hack Smart</p>
              </div>
            </div>
            
            <p className="text-gray-400 leading-relaxed mb-6">
              Empowering the next generation of digital creators through safe, engaging, 
              and educational experiences. Where learning becomes an adventure.
            </p>
            
            {/* Social Links */}
            <div className="flex gap-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  className="w-10 h-10 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Newsletter Signup */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <div className="bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 rounded-2xl p-8 border border-cyan-500/20">
              <div className="text-center mb-6">
                <h4 className="text-2xl font-bold text-white mb-3">
                  🚀 Ready to Start Your ByteHero Journey?
                </h4>
                <p className="text-gray-300 max-w-2xl mx-auto">
                  Join thousands of young learners who are building the future through collaboration, 
                  AI companionship, and hands-on tech education.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-1"
                >
                  <Button
                    onClick={onSignupClick}
                    className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3"
                  >
                    <Heart className="w-5 h-5 mr-2" />
                    Start Learning Today
                  </Button>
                </motion.div>
                <Button
                  variant="outline"
                  className="border-gray-700 text-gray-300 hover:bg-gray-800/50"
                >
                  <Mail className="w-5 h-5 mr-2" />
                  Get Updates
                </Button>
              </div>
              
              <div className="text-center mt-4">
                <p className="text-sm text-gray-400">
                  <span className="text-green-400 font-semibold">100% Free to Start</span> • 
                  <span className="text-blue-400 font-semibold"> No Credit Card Required</span> • 
                  <span className="text-purple-400 font-semibold"> Cancel Anytime</span>
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Links Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
          {footerSections.map((section, index) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
            >
              <h5 className="font-semibold text-white mb-4">{section.title}</h5>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors text-sm"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="flex flex-wrap justify-center gap-6 items-center mb-12"
        >
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Shield className="w-4 h-4 text-green-400" />
            <span>COPPA Certified</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <BookOpen className="w-4 h-4 text-blue-400" />
            <span>CSTA Aligned</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Users className="w-4 h-4 text-purple-400" />
            <span>10K+ Happy Families</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Heart className="w-4 h-4 text-pink-400" />
            <span>500+ Partner Schools</span>
          </div>
        </motion.div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800/50">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              className="text-center md:text-left"
            >
              <p className="text-gray-400 text-sm">
                &copy; 2024 ByteHero. All rights reserved. Made with{' '}
                <Heart className="w-4 h-4 inline text-red-400" />{' '}
                for young learners everywhere.
              </p>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="flex items-center gap-6 text-sm text-gray-400"
            >
              <a href="#" className="hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Terms of Service
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Accessibility
              </a>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  )
}

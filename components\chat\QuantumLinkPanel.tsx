"use client"

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useChat } from '@/contexts/ChatContext'
import { useAuth } from '@/contexts/AuthContext'
import { ChatMessage } from './ChatMessage'
import { ChannelList } from './ChannelList'
import { PresetPhrasePicker } from './PresetPhrasePicker'
import { ModerationFlag } from './ModerationFlag'
import { ReflectionPrompt } from './ReflectionPrompt'
import { CommunicationQuests } from './CommunicationQuests'
import { TimelineHarmonyMeter } from './TimelineHarmonyMeter'
import { AvatarExpression } from './AvatarExpression'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  MessageCircle,
  X,
  Send,
  Users,
  <PERSON>h,
  <PERSON>rk<PERSON>,
  <PERSON>,
  <PERSON>clip,
  Target,
  Co<PERSON>,
  Zap
} from 'lucide-react'

interface QuantumLinkPanelProps {
  isOpen: boolean
  onToggle: () => void
  className?: string
}

export function QuantumLinkPanel({ isOpen, onToggle, className = "" }: QuantumLinkPanelProps) {
  const { user } = useAuth()
  const {
    channels,
    directMessages,
    messages,
    activeChannelId,
    activeDMId,
    onlineUsers: _onlineUsers,
    notifications: _notifications,
    sendMessage,
    sendPresetPhrase,
    flagMessage,
    setActiveChannel: _setActiveChannel,
    setActiveDM: _setActiveDM,
    getAvailablePresetPhrases: _getAvailablePresetPhrases,
    submitReflection,
    claimQuestReward,
    updateUserExpression,
    activeQuests,
    pendingReflections,
    timelineHarmony,
    userCubits,
    userCE,
    communicationLevel
  } = useChat()

  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState<'channels' | 'dms' | 'users'>('channels')
  const [showPresetPicker, setShowPresetPicker] = useState(false)
  const [showFlagDialog, setShowFlagDialog] = useState<string | null>(null)
  const [showReflection, setShowReflection] = useState(false)
  const [currentExpression, setCurrentExpression] = useState<'happy' | 'focused' | 'curious' | 'helpful' | 'neutral'>('neutral')
  const [panelView, setPanelView] = useState<'chat' | 'quests' | 'harmony'>('chat')
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, activeChannelId, activeDMId])

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isOpen])

  // Show reflection prompts
  useEffect(() => {
    if (pendingReflections.length > 0 && !showReflection) {
      setShowReflection(true)
    }
  }, [pendingReflections, showReflection])

  const handleSendMessage = async () => {
    if (!messageInput.trim()) return

    try {
      await sendMessage(messageInput, activeChannelId || undefined, activeDMId || undefined)
      setMessageInput('')
    } catch (error) {
      console.error('Error sending message:', error)
      // Show preset phrase picker if message was rejected
      setShowPresetPicker(true)
    }
  }

  const handleSendPresetPhrase = async (phrase: any) => {
    try {
      await sendPresetPhrase(phrase, activeChannelId || undefined, activeDMId || undefined)
    } catch (error) {
      console.error('Error sending preset phrase:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleFlagMessage = async (flag: { messageId: string; reason: string; description?: string }) => {
    await flagMessage(flag.messageId, flag.reason, flag.description)
    setShowFlagDialog(null)
  }

  const handleReflectionSubmit = async (responseIndex: number, response: string) => {
    if (pendingReflections.length > 0) {
      await submitReflection(pendingReflections[0], responseIndex, response)
      setShowReflection(false)
    }
  }

  const activeMessages = activeChannelId 
    ? messages[activeChannelId] || []
    : activeDMId 
    ? messages[activeDMId] || []
    : []

  const totalUnreadCount = channels.reduce((sum, channel) => sum + channel.unreadCount, 0) +
                          directMessages.reduce((sum, dm) => sum + dm.unreadCount, 0)

  return (
    <>
      {/* Chat Toggle Button with Quantum Effects */}
      <motion.div
        className="fixed right-4 top-1/2 transform -translate-y-1/2 z-50"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.5 }}
      >
        <div className="relative">
          
          <Button
            onClick={onToggle}
            size="lg"
            className="relative bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white shadow-lg rounded-full w-14 h-14 p-0 overflow-hidden"
          >
            <MessageCircle className="w-6 h-6 relative z-10" />
            {totalUnreadCount > 0 && (
              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 rounded-full flex items-center justify-center z-20">
                {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
              </Badge>
            )}
          </Button>
        </div>
      </motion.div>

      {/* Main QuantumLink Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className={`fixed right-0 top-0 h-full w-96 bg-black/95 backdrop-blur-xl border-l border-gray-800/50 z-40 flex flex-col ${className}`}
          >
            {/* Quantum Background */}
          
            
            {/* Header */}
            <div className="relative z-10 flex items-center justify-between p-4 border-b border-gray-800/50">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-white font-space-grotesk">
                    QuantumLink
                  </h2>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30 text-xs">
                      <Zap className="w-3 h-3 mr-1" />
                      {userCE} CE
                    </Badge>
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs">
                      <Coins className="w-3 h-3 mr-1" />
                      {userCubits}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <AvatarExpression
                  currentExpression={currentExpression}
                  onExpressionChange={(expr) => {
                    setCurrentExpression(expr)
                    updateUserExpression(expr)
                  }}
                  avatarUrl={user?.user_metadata?.avatar_url}
                  userName={user?.user_metadata?.full_name || 'User'}
                  showPicker={true}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggle}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Panel Navigation */}
            <div className="relative z-10 flex border-b border-gray-800/50">
              {[
                { id: 'chat', label: 'Chat', icon: MessageCircle },
                { id: 'quests', label: 'Quests', icon: Target },
                { id: 'harmony', label: 'Harmony', icon: Sparkles }
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setPanelView(id as any)}
                  className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
                    panelView === id
                      ? 'text-cyan-400 border-b-2 border-cyan-400 bg-cyan-500/10'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {label}
                </button>
              ))}
            </div>

            {/* Panel Content */}
            <div className="flex-1 relative z-10">
              {panelView === 'chat' && (
                <div className="h-full flex flex-col">
                  {/* Chat Tabs */}
                  <div className="flex border-b border-gray-800/50">
                    {[
                      { id: 'channels', label: 'Channels', icon: Hash },
                      { id: 'dms', label: 'DMs', icon: MessageCircle },
                      { id: 'users', label: 'Users', icon: Users }
                    ].map(({ id, label, icon: Icon }) => (
                      <button
                        key={id}
                        onClick={() => setActiveTab(id as any)}
                        className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
                          activeTab === id
                            ? 'text-cyan-400 border-b-2 border-cyan-400'
                            : 'text-gray-400 hover:text-white'
                        }`}
                      >
                        <Icon className="w-4 h-4" />
                        {label}
                      </button>
                    ))}
                  </div>

                  <div className="flex-1 flex">
                    {/* Channel/User List */}
                    <div className="w-1/3 border-r border-gray-800/50">
                      <ChannelList
                        activeTab={activeTab}
                        searchQuery={searchQuery}
                        onSearchChange={setSearchQuery}
                      />
                    </div>

                    {/* Chat Area */}
                    <div className="flex-1 flex flex-col">
                      {(activeChannelId || activeDMId) ? (
                        <>
                          {/* Messages */}
                          <ScrollArea className="flex-1 p-4">
                            <div className="space-y-2">
                              {activeMessages.map((message) => (
                                <ChatMessage
                                  key={message.id}
                                  message={message}
                                  onReact={(messageId, emoji) => {
                                    console.log('React to message:', messageId, emoji)
                                  }}
                                  onReply={(messageId) => {
                                    console.log('Reply to message:', messageId)
                                  }}
                                  onEdit={(messageId, newContent) => {
                                    console.log('Edit message:', messageId, newContent)
                                  }}
                                  onDelete={(messageId) => {
                                    console.log('Delete message:', messageId)
                                  }}
                                />
                              ))}
                              <div ref={messagesEndRef} />
                            </div>
                          </ScrollArea>

                          {/* Message Input */}
                          <div className="p-4 border-t border-gray-800/50">
                            <div className="flex gap-2">
                              <div className="flex-1 relative">
                                <Input
                                  ref={inputRef}
                                  placeholder="Type a message..."
                                  value={messageInput}
                                  onChange={(e) => setMessageInput(e.target.value)}
                                  onKeyPress={handleKeyPress}
                                  className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 pr-24"
                                />
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowPresetPicker(true)}
                                    className="text-gray-400 hover:text-white p-1 h-auto"
                                  >
                                    <Sparkles className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-gray-400 hover:text-white p-1 h-auto"
                                  >
                                    <Paperclip className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-gray-400 hover:text-white p-1 h-auto"
                                  >
                                    <Smile className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                              <Button
                                onClick={handleSendMessage}
                                disabled={!messageInput.trim()}
                                className="bg-cyan-500 hover:bg-cyan-600 text-white"
                              >
                                <Send className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="flex-1 flex items-center justify-center text-gray-400">
                          <div className="text-center">
                            <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p>Select a channel or start a conversation</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {panelView === 'quests' && (
                <div className="h-full p-4">
                  <CommunicationQuests
                    quests={activeQuests}
                    onClaimReward={claimQuestReward}
                  />
                </div>
              )}

              {panelView === 'harmony' && (
                <div className="h-full p-4">
                  <TimelineHarmonyMeter
                    harmony={timelineHarmony}
                    recentContributions={[]}
                  />
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlays */}
      <AnimatePresence>
        {showPresetPicker && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <PresetPhrasePicker
              userLevel={communicationLevel}
              userAge="10-13"
              userCubits={userCubits}
              onSelectPhrase={handleSendPresetPhrase}
              onClose={() => setShowPresetPicker(false)}
            />
          </div>
        )}

        {showFlagDialog && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <ModerationFlag
              messageId={showFlagDialog}
              messageContent="Sample message content"
              onSubmitFlag={handleFlagMessage}
              onClose={() => setShowFlagDialog(null)}
            />
          </div>
        )}

        {showReflection && pendingReflections.length > 0 && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <ReflectionPrompt
              prompt={pendingReflections[0]}
              onSubmitResponse={handleReflectionSubmit}
              onSkip={() => setShowReflection(false)}
            />
          </div>
        )}
      </AnimatePresence>
    </>
  )
}

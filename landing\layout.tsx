import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { ParticleBackground } from '@/components/ParticleBackground'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'NanoHero - Where Young Minds Become Digital Heroes',
  description: 'Safe, educational tech experiences for young learners. Learn coding, cybersecurity, and digital citizenship through interactive games and projects.',
  keywords: 'kids coding, cybersecurity education, digital citizenship, STEM learning, educational games, safe online learning',
  authors: [{ name: 'NanoHero Team' }],
  creator: 'NanoHero',
  publisher: 'NanoHero',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://nanohero.com',
    title: 'NanoHero - Where Young Minds Become Digital Heroes',
    description: 'Safe, educational tech experiences for young learners. Learn coding, cybersecurity, and digital citizenship through interactive games and projects.',
    siteName: 'NanoHero',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'NanoHero - Digital Learning Platform for Kids',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NanoHero - Where Young Minds Become Digital Heroes',
    description: 'Safe, educational tech experiences for young learners. Learn coding, cybersecurity, and digital citizenship through interactive games and projects.',
    images: ['/og-image.png'],
    creator: '@nanohero',
  },
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0f172a',
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
  },
}

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className={`${inter.className} relative min-h-screen`}>
      {/* Background Effects */}
      <ParticleBackground />
      
      {/* Gradient Overlay */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900/50 via-gray-900/30 to-blue-900/50 pointer-events-none z-0" />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Preload critical resources */}
      <link rel="preload" href="/fonts/geist-sans.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      <link rel="preload" href="/fonts/geist-mono.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  BookOpen, 
  Calendar, 
  Clock, 
  Video,
  Plus,
  Search,
  CheckCircle,
  UserPlus,
  Link,
  Download
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  StudyGroup, 
  GroupActivity, 
  GroupResource,
  User as UserType 
} from '@/types/social'

interface StudyGroupsProps {
  currentUser: UserType
  groups: StudyGroup[]
  onJoinGroup: (groupId: string) => void
  onLeaveGroup: (groupId: string) => void
  onCreateGroup: (groupData: Partial<StudyGroup>) => void
  onStartMeeting: (groupId: string) => void
  onSubmitActivity: (activityId: string, submission: string) => void
  className?: string
}

export function StudyGroups({
  currentUser,
  groups,
  onJoinGroup,
  onLeaveGroup,
  onCreateGroup: _onCreateGroup,
  onStartMeeting,
  onSubmitActivity,
  className = ""
}: StudyGroupsProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLevel, setSelectedLevel] = useState<string | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const [_showCreateForm, setShowCreateForm] = useState(false)

  const myGroups = groups.filter(group => group.currentMembers.includes(currentUser.id))
  const availableGroups = groups.filter(group => 
    !group.currentMembers.includes(currentUser.id) &&
    group.isPublic &&
    group.currentMembers.length < group.maxMembers &&
    group.status === 'active'
  )

  const filteredGroups = availableGroups.filter(group => 
    (searchQuery === '' || 
     group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     group.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
     group.description.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (selectedLevel === null || group.level === selectedLevel) &&
    (selectedSubject === null || group.subject === selectedSubject)
  )

  const allSubjects = Array.from(new Set(groups.map(g => g.subject)))
  const levels = ['beginner', 'intermediate', 'advanced']

  const getNextMeeting = (group: StudyGroup) => {
    const _now = new Date()
    const schedule = group.meetingSchedule

    // This is a simplified calculation - in a real app, you'd use a proper scheduling library
    const _nextMeeting = new Date()
    _nextMeeting.setDate(_nextMeeting.getDate() + 1)
    _nextMeeting.setHours(parseInt(schedule.time.split(':')[0]), parseInt(schedule.time.split(':')[1]))

    return _nextMeeting
  }

  const renderGroupCard = (group: StudyGroup, isMember: boolean = false) => {
    const _nextMeeting = getNextMeeting(group)
    const completedActivities = group.activities.filter(a => a.isCompleted).length
    const totalActivities = group.activities.length
    const progress = totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0

    return (
      <motion.div
        key={group.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="group"
      >
        <Card className="bg-gray-800 border-gray-700 hover:border-cyan-400 transition-all">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h3 className="font-semibold text-white text-lg">{group.name}</h3>
                  <p className="text-sm text-gray-300 line-clamp-2">{group.description}</p>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-cyan-400 text-cyan-400">
                      {group.subject}
                    </Badge>
                    <Badge variant="outline" className="text-xs capitalize">
                      {group.level}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {group.status}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-gray-400">
                    <Users className="w-4 h-4" />
                    <span>{group.currentMembers.length}/{group.maxMembers}</span>
                  </div>
                </div>
              </div>

              {/* Progress */}
              {isMember && totalActivities > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Progress</span>
                    <span className="text-white">{completedActivities}/{totalActivities} activities</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}

              {/* Meeting Info */}
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span className="capitalize">{group.meetingSchedule.frequency}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{group.meetingSchedule.time}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Video className="w-4 h-4" />
                  <span className="capitalize">{group.meetingSchedule.meetingType}</span>
                </div>
              </div>

              {/* Goals */}
              {group.goals.length > 0 && (
                <div>
                  <span className="text-xs text-gray-400 uppercase tracking-wide">Goals</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {group.goals.slice(0, 3).map((goal, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {goal}
                      </Badge>
                    ))}
                    {group.goals.length > 3 && (
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                        +{group.goals.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-2">
                <div className="flex -space-x-2">
                  {group.currentMembers.slice(0, 4).map((memberId, index) => (
                    <Avatar key={memberId} className="w-8 h-8 border-2 border-gray-800">
                      <AvatarImage src={`/api/avatar/${memberId}`} />
                      <AvatarFallback className="text-xs">U{index + 1}</AvatarFallback>
                    </Avatar>
                  ))}
                  {group.currentMembers.length > 4 && (
                    <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-gray-800 flex items-center justify-center">
                      <span className="text-xs text-gray-300">+{group.currentMembers.length - 4}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  {isMember ? (
                    <>
                      <Button
                        size="sm"
                        onClick={() => onStartMeeting(group.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Video className="w-4 h-4 mr-1" />
                        Join Meeting
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onLeaveGroup(group.id)}
                      >
                        Leave
                      </Button>
                    </>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => onJoinGroup(group.id)}
                      className="bg-cyan-600 hover:bg-cyan-700"
                      disabled={group.currentMembers.length >= group.maxMembers}
                    >
                      <UserPlus className="w-4 h-4 mr-1" />
                      Join Group
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  const renderActivityCard = (activity: GroupActivity) => (
    <Card key={activity.id} className="bg-gray-800 border-gray-700">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-white">{activity.name}</h4>
              <Badge variant="outline" className="text-xs capitalize">
                {activity.type}
              </Badge>
              {activity.isCompleted && (
                <CheckCircle className="w-4 h-4 text-green-400" />
              )}
            </div>
            <p className="text-sm text-gray-300">{activity.description}</p>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>{activity.participants.length} participants</span>
              </div>
              {activity.dueDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Due {activity.dueDate.toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-2">
            {!activity.isCompleted && (
              <Button
                size="sm"
                onClick={() => onSubmitActivity(activity.id, '')}
                className="bg-cyan-600 hover:bg-cyan-700"
              >
                Submit
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderResourceCard = (resource: GroupResource) => (
    <Card key={resource.id} className="bg-gray-800 border-gray-700">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-white">{resource.name}</h4>
              <Badge variant="outline" className="text-xs capitalize">
                {resource.type}
              </Badge>
              {resource.isRequired && (
                <Badge variant="destructive" className="text-xs">
                  Required
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <span>Added by User</span>
              <span>{resource.addedAt.toLocaleDateString()}</span>
              {resource.dueDate && (
                <span>Due {resource.dueDate.toLocaleDateString()}</span>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            {resource.url && (
              <Button size="sm" variant="outline">
                <Link className="w-4 h-4" />
              </Button>
            )}
            <Button size="sm" variant="outline">
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Study Groups</h1>
          <p className="text-gray-400 mt-1">Join collaborative learning communities</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-cyan-600 hover:bg-cyan-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      <Tabs defaultValue="my-groups" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-800">
          <TabsTrigger value="my-groups">My Groups ({myGroups.length})</TabsTrigger>
          <TabsTrigger value="discover">Discover Groups</TabsTrigger>
          <TabsTrigger value="activities">Activities</TabsTrigger>
        </TabsList>

        <TabsContent value="my-groups" className="space-y-6">
          {myGroups.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {myGroups.map(group => renderGroupCard(group, true))}
            </div>
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No study groups yet</h3>
                <p className="text-gray-400 mb-4">Join or create a study group to start learning with others</p>
                <Button
                  onClick={() => setShowCreateForm(true)}
                  className="bg-cyan-600 hover:bg-cyan-700"
                >
                  Create Your First Group
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="discover" className="space-y-6">
          {/* Search and Filters */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search study groups..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                </div>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    variant={selectedSubject ? "outline" : "default"}
                    size="sm"
                    onClick={() => setSelectedSubject(null)}
                  >
                    All Subjects
                  </Button>
                  {allSubjects.slice(0, 5).map((subject) => (
                    <Button
                      key={subject}
                      variant={selectedSubject === subject ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedSubject(selectedSubject === subject ? null : subject)}
                    >
                      {subject}
                    </Button>
                  ))}
                </div>
                <div className="flex gap-2">
                  {levels.map((level) => (
                    <Button
                      key={level}
                      variant={selectedLevel === level ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedLevel(selectedLevel === level ? null : level)}
                      className="capitalize"
                    >
                      {level}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Groups Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredGroups.map(group => renderGroupCard(group, false))}
          </div>

          {filteredGroups.length === 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No groups found</h3>
                <p className="text-gray-400">Try adjusting your search criteria or create a new group</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activities" className="space-y-6">
          {/* Recent Activities */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Recent Activities</h3>
            {myGroups.flatMap(group => group.activities).slice(0, 5).map(renderActivityCard)}
          </div>

          {/* Resources */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Shared Resources</h3>
            {myGroups.flatMap(group => group.resources).slice(0, 5).map(renderResourceCard)}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default StudyGroups

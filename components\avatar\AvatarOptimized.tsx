"use client"

import React, { Suspense, useMemo, useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, useGLTF, Detailed } from '@react-three/drei'
import * as THREE from 'three'
import { Avatar3DProps, AvatarCustomization, Avatar3DSettings } from '@/types/avatar'
import { Loader2 } from 'lucide-react'

// Performance monitoring hook
function usePerformanceMonitor() {
  const [fps, setFps] = useState(60)
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())

  useFrame(() => {
    frameCount.current++
    const currentTime = performance.now()
    
    if (currentTime - lastTime.current >= 1000) {
      setFps(frameCount.current)
      frameCount.current = 0
      lastTime.current = currentTime
    }
  })

  return fps
}

// LOD (Level of Detail) Avatar Model
function LODAvatarModel({ 
  customization, 
  settings,
  onLoad, 
  onError: _onError
}: {
  customization: AvatarCustomization
  settings: Avatar3DSettings
  onLoad?: () => void
  onError?: (error: Error) => void
}) {
  const meshRef = useRef<THREE.Group>(null)
  const [mixer, setMixer] = useState<THREE.AnimationMixer | null>(null)
  const fps = usePerformanceMonitor()
  
  // Dynamic quality adjustment based on performance
  const dynamicQuality = useMemo(() => {
    if (fps < 30) return 'low'
    if (fps < 45) return 'medium'
    return settings.quality
  }, [fps, settings.quality])

  // LOD models - different quality levels
  const avatarUrl = customization.readyPlayerMeUrl || '/models/default-avatar.glb'
  const lowQualityUrl = avatarUrl.replace('.glb', '-low.glb')
  const mediumQualityUrl = avatarUrl.replace('.glb', '-medium.glb')
  const highQualityUrl = avatarUrl

  // Load models with error handling - always call hooks unconditionally
  // Always load all models unconditionally (hooks must be called in same order)
  const lowModel = useGLTF(lowQualityUrl)
  const mediumModel = useGLTF(mediumQualityUrl)
  const highModel = useGLTF(highQualityUrl)

  // Select the appropriate model based on quality
  let _selectedModel = lowModel
  if (dynamicQuality === 'medium' && mediumModel) {
    _selectedModel = mediumModel
  } else if (dynamicQuality === 'high' && highModel) {
    _selectedModel = highModel
  }

  // Optimized animation system
  useEffect(() => {
    const model = highModel || mediumModel || lowModel
    if (model && model.animations && model.animations.length > 0 && settings.enableAnimations) {
      const newMixer = new THREE.AnimationMixer(model.scene)
      setMixer(newMixer)
      
      // Play idle animation with reduced update frequency for performance
      const idleClip = model.animations.find((clip: THREE.AnimationClip) =>
        clip.name.toLowerCase().includes('idle') ||
        clip.name.toLowerCase().includes(customization.idleAnimation)
      )
      
      if (idleClip) {
        const action = newMixer.clipAction(idleClip)
        action.play()
      }
      
      onLoad?.()
      
      return () => {
        newMixer.stopAllAction()
      }
    }
  }, [highModel, mediumModel, lowModel, customization.idleAnimation, settings.enableAnimations, onLoad])

  // Throttled animation updates for performance
  const animationUpdateRate = useMemo(() => {
    if (fps < 30) return 0.5 // Update at half rate
    if (fps < 45) return 0.75 // Update at 3/4 rate
    return 1 // Full rate
  }, [fps])

  useFrame((state, delta) => {
    if (mixer && settings.enableAnimations) {
      mixer.update(delta * animationUpdateRate)
    }
    
    // Subtle floating animation (optimized)
    if (meshRef.current && settings.enableAnimations) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.05
    }
  })

  // Optimized fallback avatar with reduced geometry
  const OptimizedFallbackAvatar = useMemo(() => (
    <group ref={meshRef}>
      {/* Simplified geometry for better performance */}
      <mesh position={[0, 1.6, 0]}>
        <sphereGeometry args={[0.15, 16, 16]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      <mesh position={[0, 1, 0]}>
        <cylinderGeometry args={[0.2, 0.25, 0.8, 6]} />
        <meshStandardMaterial color={customization.outfit === 'cyberpunk' ? '#00ffff' : '#4a5568'} />
      </mesh>
      
      {/* Simplified limbs */}
      <mesh position={[-0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 6]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      <mesh position={[0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 6]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      <mesh position={[-0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 6]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      <mesh position={[0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 6]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      
      {/* Simplified hair */}
      <mesh position={[0, 1.75, 0]}>
        <sphereGeometry args={[0.18, 12, 12]} />
        <meshStandardMaterial color={customization.hairColor} />
      </mesh>
    </group>
  ), [customization])

  // Use LOD system for automatic quality switching
  if (settings.enableLOD && (highModel || mediumModel || lowModel)) {
    return (
      <Detailed distances={[0, 5, 10]}>
        {/* High quality - close up */}
        {highModel ? (
          <primitive
            ref={meshRef}
            object={highModel.scene.clone()}
            scale={[customization.height, customization.height, customization.height]}
          />
        ) : (
          <group />
        )}

        {/* Medium quality - medium distance */}
        {mediumModel ? (
          <primitive
            ref={meshRef}
            object={mediumModel.scene.clone()}
            scale={[customization.height, customization.height, customization.height]}
          />
        ) : (
          <group />
        )}

        {/* Low quality - far distance */}
        {lowModel ? (
          <primitive
            ref={meshRef}
            object={lowModel.scene.clone()}
            scale={[customization.height, customization.height, customization.height]}
          />
        ) : (
          <group />
        )}
      </Detailed>
    )
  }

  // Single model or fallback
  const model = highModel || mediumModel || lowModel
  if (model && model.scene) {
    return (
      <primitive 
        ref={meshRef}
        object={model.scene} 
        scale={[customization.height, customization.height, customization.height]}
      />
    )
  }

  return OptimizedFallbackAvatar
}

// Optimized loading component
function OptimizedAvatarLoader() {
  return (
    <div className="flex items-center justify-center h-full bg-gray-900/50">
      <div className="text-center">
        <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-cyan-400" />
        <p className="text-xs text-gray-400">Loading avatar...</p>
      </div>
    </div>
  )
}

// Main optimized Avatar 3D component
export function AvatarOptimized({
  customization,
  settings,
  animationState: _animationState,
  onLoad,
  onError,
  className = "",
  style
}: Avatar3DProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  // Optimized lighting based on quality settings
  const lightingConfig = useMemo(() => {
    switch (settings.quality) {
      case 'low':
        return {
          ambientIntensity: settings.ambientLightIntensity * 0.8,
          directionalIntensity: settings.directionalLightIntensity * 0.8,
          shadows: false
        }
      case 'medium':
        return {
          ambientIntensity: settings.ambientLightIntensity,
          directionalIntensity: settings.directionalLightIntensity,
          shadows: true
        }
      case 'high':
        return {
          ambientIntensity: settings.ambientLightIntensity * 1.2,
          directionalIntensity: settings.directionalLightIntensity * 1.2,
          shadows: true
        }
      default:
        return {
          ambientIntensity: settings.ambientLightIntensity,
          directionalIntensity: settings.directionalLightIntensity,
          shadows: true
        }
    }
  }, [settings])

  return (
    <div 
      ref={containerRef}
      className={`relative w-full h-full bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden ${className}`} 
      style={style}
    >
      {isVisible && (
        <Canvas
          shadows={lightingConfig.shadows}
          camera={{ position: [0, 0, settings.cameraDistance], fov: 50 }}
          gl={{ 
            antialias: settings.quality !== 'low', 
            alpha: true,
            powerPreference: "high-performance"
          }}
          performance={{ min: 0.5 }}
        >
          <Suspense fallback={null}>
            {/* Optimized lighting */}
            <ambientLight intensity={lightingConfig.ambientIntensity} />
            <directionalLight 
              position={[5, 5, 5]} 
              intensity={lightingConfig.directionalIntensity}
              castShadow={lightingConfig.shadows}
              shadow-mapSize-width={settings.quality === 'high' ? 2048 : 1024}
              shadow-mapSize-height={settings.quality === 'high' ? 2048 : 1024}
            />
            
            {/* Environment - simplified for performance */}
            {settings.quality === 'high' && <Environment preset="city" />}
            
            {/* Avatar Model with LOD */}
            <LODAvatarModel
              customization={customization}
              settings={settings}
              onLoad={handleLoad}
              onError={onError}
            />
            
            {/* Optimized ground shadow */}
            {lightingConfig.shadows && (
              <ContactShadows 
                position={[0, 0, 0]} 
                opacity={0.3} 
                scale={2} 
                blur={1.5} 
                far={1.5} 
              />
            )}
            
            {/* Camera controls with performance optimizations */}
            <OrbitControls
              enablePan={false}
              enableZoom={true}
              enableRotate={true}
              autoRotate={settings.autoRotate}
              autoRotateSpeed={1}
              minDistance={1}
              maxDistance={10}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI - Math.PI / 6}
              enableDamping={settings.quality !== 'low'}
              dampingFactor={0.05}
            />
          </Suspense>
        </Canvas>
      )}

      {/* Loading overlay */}
      {isVisible && !isLoaded && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <OptimizedAvatarLoader />
        </div>
      )}

      {/* Performance indicator (dev mode) */}
      {process.env.NODE_ENV === 'development' && isLoaded && (
        <div className="absolute top-2 left-2 bg-black/70 rounded px-2 py-1 text-xs text-white">
          Quality: {settings.quality}
        </div>
      )}
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import {
  Flag,
  Shield,
  AlertTriangle,
  MessageSquare,
  Ban,
  User,
  X
} from 'lucide-react'

interface ModerationFlagProps {
  messageId: string
  messageContent: string
  onSubmitFlag: (flag: {
    messageId: string
    reason: string
    description?: string
  }) => void
  onClose: () => void
  className?: string
}

const flagReasons = [
  {
    id: 'inappropriate',
    label: 'Inappropriate Language',
    description: 'Contains words that are not kind or respectful',
    icon: AlertTriangle,
    color: 'text-red-400'
  },
  {
    id: 'bullying',
    label: 'Bullying or Mean Behavior',
    description: 'Someone is being mean or hurtful to others',
    icon: User,
    color: 'text-orange-400'
  },
  {
    id: 'spam',
    label: 'Spam or Repeated Messages',
    description: 'Same message sent many times or irrelevant content',
    icon: Ban,
    color: 'text-yellow-400'
  },
  {
    id: 'personal_info',
    label: 'Sharing Personal Information',
    description: 'Someone shared private details like name, school, or contact info',
    icon: Shield,
    color: 'text-purple-400'
  },
  {
    id: 'other',
    label: 'Other Concern',
    description: 'Something else that doesn\'t feel right or safe',
    icon: Flag,
    color: 'text-gray-400'
  }
]

export function ModerationFlag({
  messageId,
  messageContent,
  onSubmitFlag,
  onClose,
  className = ""
}: ModerationFlagProps) {
  const [selectedReason, setSelectedReason] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!selectedReason) return

    setIsSubmitting(true)
    
    try {
      await onSubmitFlag({
        messageId,
        reason: selectedReason,
        description: description.trim() || undefined
      })
      onClose()
    } catch (error) {
      console.error('Error submitting flag:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`bg-black/95 backdrop-blur-xl border border-gray-800/50 rounded-xl p-6 max-w-md w-full ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
            <Flag className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white font-space-grotesk">
              Report Message
            </h3>
            <p className="text-sm text-gray-400">
              Help keep our community safe
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-gray-400 hover:text-white"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Message Preview */}
      <div className="mb-6 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <MessageSquare className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-400">Reported Message:</span>
        </div>
        <p className="text-sm text-gray-300 italic">
          &quot;{messageContent.length > 100 ? `${messageContent.substring(0, 100)}...` : messageContent}&quot;
        </p>
      </div>

      {/* Reason Selection */}
      <div className="mb-6">
        <Label className="text-white font-medium mb-3 block">
          Why are you reporting this message?
        </Label>
        <RadioGroup value={selectedReason} onValueChange={setSelectedReason}>
          <div className="space-y-3">
            {flagReasons.map((reason) => {
              const Icon = reason.icon
              return (
                <motion.div
                  key={reason.id}
                  whileHover={{ scale: 1.02 }}
                  className={`flex items-start space-x-3 p-3 rounded-lg border transition-all cursor-pointer ${
                    selectedReason === reason.id
                      ? 'border-cyan-500/50 bg-cyan-500/10'
                      : 'border-gray-700 bg-gray-800/30 hover:border-gray-600'
                  }`}
                  onClick={() => setSelectedReason(reason.id)}
                >
                  <RadioGroupItem 
                    value={reason.id} 
                    id={reason.id}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label 
                      htmlFor={reason.id}
                      className="flex items-center gap-2 text-white font-medium cursor-pointer"
                    >
                      <Icon className={`w-4 h-4 ${reason.color}`} />
                      {reason.label}
                    </Label>
                    <p className="text-sm text-gray-400 mt-1">
                      {reason.description}
                    </p>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </RadioGroup>
      </div>

      {/* Additional Description */}
      {selectedReason && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mb-6"
        >
          <Label className="text-white font-medium mb-2 block">
            Additional Details (Optional)
          </Label>
          <Textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Can you tell us more about why this message concerns you?"
            className="bg-gray-800/50 border-gray-700 text-white placeholder-gray-400 resize-none"
            rows={3}
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {description.length}/500 characters
          </p>
        </motion.div>
      )}

      {/* Actions */}
      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onClose}
          className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!selectedReason || isSubmitting}
          className="flex-1 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              Reporting...
            </div>
          ) : (
            <>
              <Flag className="w-4 h-4 mr-2" />
              Submit Report
            </>
          )}
        </Button>
      </div>

      {/* Help Text */}
      <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
        <p className="text-blue-400 text-xs">
          🛡️ <strong>Remember:</strong> Reporting helps keep everyone safe. Our moderators will review this message and take appropriate action. Thank you for helping build a positive community!
        </p>
      </div>
    </motion.div>
  )
}

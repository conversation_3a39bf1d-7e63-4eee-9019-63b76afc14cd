"use client"

import React from 'react'
import { Textarea } from '@/components/ui/textarea'

interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
}

export default function RichTextEditor({
  value = '',
  onChange,
  placeholder = 'Enter your text here...',
  className = ''
}: RichTextEditorProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="text-sm text-gray-400 mb-2">
        Rich Text Editor (Placeholder)
      </div>
      <Textarea
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        className="min-h-[200px] bg-gray-800/50 border-gray-600 text-white placeholder:text-gray-400"
      />
    </div>
  )
}

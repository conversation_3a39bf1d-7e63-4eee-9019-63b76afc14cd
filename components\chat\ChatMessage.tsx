"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Reply, 
  Edit, 
  Trash2, 
  Heart,
  ThumbsUp,
  Copy
} from 'lucide-react'
import { ChatMessage as ChatMessageType } from '@/types/chat'
import { formatDistanceToNow } from 'date-fns'

interface ChatMessageProps {
  message: ChatMessageType
  onReact?: (messageId: string, emoji: string) => void
  onReply?: (messageId: string) => void
  onEdit?: (messageId: string, newContent: string) => void
  onDelete?: (messageId: string) => void
  className?: string
}

export function ChatMessage({
  message,
  onReact,
  onReply,
  onEdit,
  onDelete,
  className = ""
}: ChatMessageProps) {
  const { user } = useAuth()
  const [showActions, setShowActions] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(message.content)

  const isOwnMessage = user?.id === message.senderId
  const timeAgo = formatDistanceToNow(new Date(message.timestamp), { addSuffix: true })

  const handleReact = (emoji: string) => {
    onReact?.(message.id, emoji)
  }

  const handleEdit = () => {
    if (editContent.trim() && editContent !== message.content) {
      onEdit?.(message.id, editContent.trim())
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditContent(message.content)
    setIsEditing(false)
  }

  const getReactionCount = (emoji: string) => {
    return message.reactions.filter(r => r.emoji === emoji).length
  }

  const hasUserReacted = (emoji: string) => {
    return message.reactions.some(r => r.emoji === emoji && r.userId === user?.id)
  }

  const uniqueReactions = Array.from(new Set(message.reactions.map(r => r.emoji)))

  if (message.isDeleted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex gap-3 py-2 ${className}`}
      >
        <Avatar className="w-8 h-8 opacity-50">
          <AvatarFallback className="text-xs bg-gray-700">
            {message.senderName.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-500">
              {message.senderName}
            </span>
            <span className="text-xs text-gray-500">{timeAgo}</span>
          </div>
          <p className="text-sm text-gray-500 italic">This message was deleted</p>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      className={`group flex gap-3 py-2 px-3 rounded-lg hover:bg-gray-800/30 transition-colors relative ${className}`}
    >
      <Avatar className="w-8 h-8">
        <AvatarImage src={message.senderAvatar} />
        <AvatarFallback className="text-xs bg-gradient-to-r from-cyan-500 to-purple-500 text-white">
          {message.senderName.charAt(0)}
        </AvatarFallback>
      </Avatar>

      <div className="flex-1 min-w-0">
        {/* Message Header */}
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium text-white">
            {message.senderName}
          </span>
          {isOwnMessage && (
            <Badge variant="secondary" className="text-xs bg-cyan-500/20 text-cyan-400">
              You
            </Badge>
          )}
          <span className="text-xs text-gray-400">{timeAgo}</span>
          {message.edited && (
            <span className="text-xs text-gray-500">(edited)</span>
          )}
        </div>

        {/* Message Content */}
        {isEditing ? (
          <div className="space-y-2">
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full p-2 text-sm bg-gray-900/50 border border-gray-700 rounded text-white resize-none"
              rows={3}
              autoFocus
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleEdit} className="bg-cyan-500 hover:bg-cyan-600">
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-sm text-gray-300 whitespace-pre-wrap break-words">
              {message.content}
            </p>

            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <div className="space-y-2">
                {message.attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center gap-2 p-2 bg-gray-800/50 rounded border border-gray-700"
                  >
                    <span className="text-sm text-gray-300">{attachment.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {attachment.type}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            {/* Reactions */}
            {uniqueReactions.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {uniqueReactions.map((emoji) => (
                  <button
                    key={emoji}
                    onClick={() => handleReact(emoji)}
                    className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors ${
                      hasUserReacted(emoji)
                        ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                        : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'
                    }`}
                  >
                    <span>{emoji}</span>
                    <span>{getReactionCount(emoji)}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Message Actions */}
      {showActions && !isEditing && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute top-0 right-2 flex items-center gap-1 bg-gray-900/90 backdrop-blur-sm border border-gray-700 rounded-lg p-1"
        >
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleReact('👍')}
            className="text-gray-400 hover:text-white p-1 h-auto"
          >
            <ThumbsUp className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleReact('❤️')}
            className="text-gray-400 hover:text-white p-1 h-auto"
          >
            <Heart className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onReply?.(message.id)}
            className="text-gray-400 hover:text-white p-1 h-auto"
          >
            <Reply className="w-3 h-3" />
          </Button>
          {isOwnMessage && (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsEditing(true)}
                className="text-gray-400 hover:text-white p-1 h-auto"
              >
                <Edit className="w-3 h-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDelete?.(message.id)}
                className="text-gray-400 hover:text-red-400 p-1 h-auto"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </>
          )}
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigator.clipboard.writeText(message.content)}
            className="text-gray-400 hover:text-white p-1 h-auto"
          >
            <Copy className="w-3 h-3" />
          </Button>
        </motion.div>
      )}
    </motion.div>
  )
}

"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { QuantumArrivalHero } from './QuantumArrivalHero'
import { DeCodeLearningSection } from './DeCodeLearningSection'
import { GameLikeJourneyTrail } from './GameLikeJourneyTrail'
import { DigitalTrustSection } from './DigitalTrustSection'
import { InteractiveByteverseMap } from './InteractiveByteverseMap'
import { CallToAdventureQuest } from './CallToAdventureQuest'
import { AISidekickByteSection } from './AISidekickByteSection'
import { GamifiedSignUpModal } from './GamifiedSignUpModal'
import { ByteHeroNavigation } from './ByteHeroNavigation'
import { ByteHeroFooter } from './ByteHeroFooter'
import ParticleBackground from '@/components/ParticleBackground'
import { useOptimizedScroll } from '@/utils/eventOptimization'

interface ByteHeroLandingPageProps {
  onLogin?: () => void
}

export function ByteHeroLandingPage({ onLogin }: ByteHeroLandingPageProps) {
  const [showSignupModal, setShowSignupModal] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [showFloatingCTA, setShowFloatingCTA] = useState(false)
  const [currentSection, setCurrentSection] = useState('hero')

  // Optimized scroll handling
  useOptimizedScroll(({ scrollY: _scrollY, progress }) => {
    setScrollProgress(progress)

    // Show floating CTA after 30% scroll
    setShowFloatingCTA(progress > 0.3 && progress < 0.9)

    // Update current section for navigation (throttled)
    const sections = ['hero', 'decode', 'journey', 'safety', 'map', 'quest', 'byte']
    const sectionElements = sections.map(id => document.getElementById(id))

    for (let i = sections.length - 1; i >= 0; i--) {
      const element = sectionElements[i]
      if (element && element.getBoundingClientRect().top <= 100) {
        setCurrentSection(sections[i])
        break
      }
    }
  }, 16) // 60fps throttling

  const handleSignupOpen = () => {
    setShowSignupModal(true)
  }

  const handleSignupClose = () => {
    setShowSignupModal(false)
  }

  const handleSignupComplete = (userData: any) => {
    console.log('User signed up:', userData)
    setShowSignupModal(false)
    onLogin?.()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-dark via-black to-space-blue text-white overflow-x-hidden">
      {/* Quantum Particle Background */}
      <ParticleBackground />

      {/* Progress indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 z-50 origin-left"
        style={{ scaleX: scrollProgress }}
      />

      <div className="relative z-10">
        {/* Navigation */}
        <ByteHeroNavigation
          currentSection={currentSection}
          onSignupClick={handleSignupOpen}
          onLogin={onLogin}
        />

      {/* Hero Section - Quantum Arrival */}
      <section id="hero">
        <QuantumArrivalHero onScrollToNext={() => {
          document.getElementById('decode')?.scrollIntoView({ behavior: 'smooth' })
        }} />
      </section>

      {/* How It Works Section - DeCode Your Learning */}
      <section id="decode">
        <DeCodeLearningSection onSignupClick={handleSignupOpen} />
      </section>

      {/* Game-Like Features Teaser - Your Journey Begins Here */}
      <section id="journey">
        <GameLikeJourneyTrail onSignupClick={handleSignupOpen} />
      </section>

      {/* Why It's Safe - Digital Trust Section */}
      <section id="safety">
        <DigitalTrustSection />
      </section>

      {/* Explore the World - Interactive Map Snapshot */}
      <section id="map">
        <InteractiveByteverseMap onSignupClick={handleSignupOpen} />
      </section>

      {/* Call to Adventure Section - Your First Quest */}
      <section id="quest">
        <CallToAdventureQuest onSignupClick={handleSignupOpen} />
      </section>

      {/* AI Sidekick Section - Meet Byte */}
      <section id="byte">
        <AISidekickByteSection onSignupClick={handleSignupOpen} />
      </section>

        {/* Footer */}
        <ByteHeroFooter onSignupClick={handleSignupOpen} />
      </div>

      {/* Floating CTA */}
      <AnimatePresence>
        {showFloatingCTA && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 100, scale: 0.8 }}
            className="fixed bottom-8 right-8 z-40"
          >
            <motion.button
              onClick={handleSignupOpen}
              className="bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 p-1 rounded-2xl shadow-2xl"
              animate={{
                boxShadow: [
                  "0 0 20px rgba(139, 92, 246, 0.3)",
                  "0 0 40px rgba(59, 130, 246, 0.4)",
                  "0 0 20px rgba(34, 211, 238, 0.3)",
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="bg-black/90 backdrop-blur-xl rounded-2xl px-6 py-3 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">⚡</span>
                </div>
                <div className="text-left">
                  <p className="text-white font-bold text-sm">Ready to Begin?</p>
                  <p className="text-gray-300 text-xs">Start Your Quest</p>
                </div>
              </div>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gamified Sign-Up Modal */}
      <GamifiedSignUpModal
        isOpen={showSignupModal}
        onClose={handleSignupClose}
        onComplete={handleSignupComplete}
      />
    </div>
  )
}

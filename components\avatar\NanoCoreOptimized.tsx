"use client"

import React, { Suspense, useRef, useState, useEffect, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, ContactShadows } from '@react-three/drei'
import * as THREE from 'three'
import { useThreeCleanup } from '@/utils/threeCleanup'
import { 
  NanoCoreProps, 
  NanoCoreCustomization, 
  DEFAULT_NANOCORE_CUSTOMIZATION,
  DEFAULT_AVATAR_SETTINGS 
} from '@/types/avatar'
import { Loader2 } from 'lucide-react'

// Performance monitoring hook
function usePerformanceMonitor() {
  const [fps, setFps] = useState(60)
  const [frameTime, setFrameTime] = useState(16.67)
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  
  useFrame(() => {
    frameCount.current++
    const currentTime = performance.now()
    const deltaTime = currentTime - lastTime.current
    
    if (deltaTime >= 1000) { // Update every second
      const currentFps = (frameCount.current * 1000) / deltaTime
      setFps(Math.round(currentFps))
      setFrameTime(1000 / currentFps)
      
      frameCount.current = 0
      lastTime.current = currentTime
    }
  })
  
  return { fps, frameTime }
}

// LOD (Level of Detail) NanoCore Model
function LODNanoCoreModel({
  customization,
  onLoad,
  onError: _onError
}: {
  customization: NanoCoreCustomization
  onLoad?: () => void
  onError?: (error: Error) => void
}) {
  const groupRef = useRef<THREE.Group>(null)
  const [time, setTime] = useState(0)
  const { fps } = usePerformanceMonitor()
  const { addCleanupRef, cleanup } = useThreeCleanup()

  // Add cleanup ref and cleanup on unmount
  useEffect(() => {
    if (groupRef.current) {
      addCleanupRef(groupRef)
    }
    return () => {
      cleanup()
    }
  }, [addCleanupRef, cleanup])
  
  // Adjust quality based on performance
  const qualityLevel = useMemo(() => {
    if (fps < 30) return 'low'
    if (fps < 45) return 'medium'
    return 'high'
  }, [fps])
  
  useFrame((state, delta) => {
    setTime(time + delta * 0.5) // Slower time progression

    if (groupRef.current) {
      // Significantly reduce animation complexity
      const animationIntensity = qualityLevel === 'low' ? 0.2 : qualityLevel === 'medium' ? 0.5 : 0.8

      // Less frequent position updates
      if (Math.floor(time * 10) % 2 === 0) {
        groupRef.current.position.y = Math.sin(time * 1.5) * 0.03 * animationIntensity
      }

      // Conditional rotation only for high quality
      if (qualityLevel === 'high') {
        groupRef.current.rotation.y += delta * 0.05 // Slower rotation
      }
    }
  })
  
  useEffect(() => {
    onLoad?.()
  }, [onLoad])
  
  // Optimized geometries for different LOD levels
  const getGeometryByLOD = (baseGeometry: React.ReactNode, lodLevel: string, componentType: string) => {
    switch (lodLevel) {
      case 'low':
        // Ultra-low poly for performance
        if (componentType === 'head') return <sphereGeometry args={[0.3, 6, 6]} />
        if (componentType === 'core') return <sphereGeometry args={[0.2, 6, 6]} />
        return <boxGeometry args={[0.2, 0.2, 0.2]} />
      case 'medium':
        // Medium poly
        if (componentType === 'head') return <sphereGeometry args={[0.3, 10, 10]} />
        if (componentType === 'core') return <sphereGeometry args={[0.2, 10, 10]} />
        return <boxGeometry args={[0.2, 0.3, 0.2]} />
      default:
        return baseGeometry
    }
  }
  
  // Optimized core components with aggressive LOD
  const CoreComponents = useMemo(() => {
    const { headUnit, visualSensors, torsoFrame, energyCore } = customization

    return (
      <group ref={groupRef}>
        {/* Simplified Head Unit - single LOD based on quality */}
        <mesh position={[0, 1.2, 0]}>
          {getGeometryByLOD(
            <sphereGeometry args={[0.3 * headUnit.size, 16, 16]} />,
            qualityLevel,
            'head'
          )}
          {qualityLevel === 'low' ? (
            <meshBasicMaterial color={customization.coreColor} />
          ) : (
            <meshStandardMaterial
              color={customization.coreColor}
              emissive={customization.coreColor}
              emissiveIntensity={customization.coreGlow * (qualityLevel === 'high' ? 0.5 : 0.2)}
              metalness={qualityLevel === 'high' && headUnit.material === 'metal' ? 0.6 : 0.1}
              roughness={0.5}
            />
          )}
        </mesh>
        
        {/* Visual Sensors - only for medium/high quality */}
        {qualityLevel === 'high' && (
          <group position={[0, 1.2, 0.25]}>
            <mesh position={[-0.08, 0, 0]}>
              <sphereGeometry args={[0.04, 6, 6]} /> {/* Reduced complexity */}
              <meshBasicMaterial color={visualSensors.color} /> {/* Simplified material */}
            </mesh>
            <mesh position={[0.08, 0, 0]}>
              <sphereGeometry args={[0.04, 6, 6]} />
              <meshBasicMaterial color={visualSensors.color} />
            </mesh>
          </group>
        )}

        {/* Simplified Torso Frame */}
        <mesh position={[0, 0.5, 0]}>
          {getGeometryByLOD(
            <cylinderGeometry args={[0.25, 0.3, 0.8, 8]} />,
            qualityLevel,
            'torso'
          )}
          {qualityLevel === 'low' ? (
            <meshBasicMaterial color={torsoFrame.color} />
          ) : (
            <meshStandardMaterial
              color={torsoFrame.color}
              metalness={qualityLevel === 'high' ? 0.5 : 0.2}
              roughness={0.4}
            />
          )}
        </mesh>

        {/* Energy Core - conditional rendering */}
        {qualityLevel !== 'low' && (
          <mesh position={[0, 0.6, 0.2]}>
            {getGeometryByLOD(
              <sphereGeometry args={[0.08, 12, 12]} />,
              qualityLevel,
              'core'
            )}
            <meshStandardMaterial
              color={energyCore.color}
              emissive={energyCore.color}
              emissiveIntensity={energyCore.intensity * 0.5} // Reduced intensity
              transparent
              opacity={0.6} // Reduced opacity
            />
          </mesh>
        )}
        
        {/* Simplified Limbs */}
        <group>
          <mesh position={[-0.4, 0.4, 0]}>
            <cylinderGeometry args={[0.06, 0.08, 0.6, qualityLevel === 'high' ? 8 : 4]} />
            <meshStandardMaterial color="#2d3748" />
          </mesh>
          <mesh position={[0.4, 0.4, 0]}>
            <cylinderGeometry args={[0.06, 0.08, 0.6, qualityLevel === 'high' ? 8 : 4]} />
            <meshStandardMaterial color="#2d3748" />
          </mesh>
          <mesh position={[-0.15, -0.2, 0]}>
            <cylinderGeometry args={[0.08, 0.1, 0.6, qualityLevel === 'high' ? 8 : 4]} />
            <meshStandardMaterial color="#2d3748" />
          </mesh>
          <mesh position={[0.15, -0.2, 0]}>
            <cylinderGeometry args={[0.08, 0.1, 0.6, qualityLevel === 'high' ? 8 : 4]} />
            <meshStandardMaterial color="#2d3748" />
          </mesh>
        </group>
        
        {/* Particle Effects - only on high quality */}
        {qualityLevel === 'high' && customization.evolutionStage.visualUpgrades.particleEffects && (
          <group>
            {Array.from({ length: 4 }).map((_, i) => (
              <mesh
                key={i}
                position={[
                  Math.cos((time + i) * 0.5) * 1.5,
                  Math.sin((time + i) * 0.3) * 0.5 + 1,
                  Math.sin((time + i) * 0.5) * 1.5
                ]}
              >
                <sphereGeometry args={[0.02, 4, 4]} />
                <meshBasicMaterial 
                  color={energyCore.color}
                  transparent
                  opacity={0.6}
                />
              </mesh>
            ))}
          </group>
        )}
      </group>
    )
  }, [customization, qualityLevel, time])
  
  return CoreComponents
}

// Performance stats overlay
function PerformanceStats({ fps, frameTime }: { fps: number; frameTime: number }) {
  return (
    <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-2 text-xs text-white">
      <div>FPS: {fps}</div>
      <div>Frame: {frameTime.toFixed(1)}ms</div>
    </div>
  )
}

// Optimized NanoCore Component
export function NanoCoreOptimized({
  customization = DEFAULT_NANOCORE_CUSTOMIZATION,
  settings = DEFAULT_AVATAR_SETTINGS,
  onLoad,
  onError,
  className = "",
  style
}: NanoCoreProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [performanceStats] = useState({ fps: 60, frameTime: 16.67 })
  
  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }
  
  // Adaptive quality settings based on device capabilities
  const adaptiveSettings = useMemo(() => {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    
    if (!gl) {
      return { ...settings, quality: 'low', enableLOD: true }
    }
    
    // Type guard to ensure we have WebGL context
    if (!('getParameter' in gl) || !('RENDERER' in gl)) {
      return { ...settings, quality: 'low', enableLOD: true }
    }

    const webGl = gl as WebGLRenderingContext
    const renderer = webGl.getParameter(webGl.RENDERER) as string
    const isLowEnd = renderer.includes('Intel') || renderer.includes('Mali')
    
    return {
      ...settings,
      quality: isLowEnd ? 'medium' : settings.quality,
      enableLOD: true,
      enablePhysics: !isLowEnd && settings.enablePhysics
    }
  }, [settings])
  
  return (
    <div className={`relative w-full h-full bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden ${className}`} style={style}>
      {/* Highly optimized 3D Canvas */}
      <Canvas
        shadows={false} // Disabled for performance
        camera={{ position: [0, 0, adaptiveSettings.cameraDistance], fov: 50 }}
        gl={{
          antialias: false, // Disabled for performance
          alpha: true,
          powerPreference: "low-power", // Changed to low-power
          stencil: false,
          depth: true
        }}
        performance={{ min: 0.3 }} // Lower threshold
        frameloop="demand" // Only render when needed
        dpr={[1, 1.5]} // Limit pixel ratio
      >
        <Suspense fallback={null}>
          {/* Minimal lighting */}
          <ambientLight intensity={adaptiveSettings.ambientLightIntensity * 0.7} />
          {adaptiveSettings.quality === 'high' && (
            <directionalLight
              position={[5, 5, 5]}
              intensity={adaptiveSettings.directionalLightIntensity * 0.6}
              castShadow={false} // Disabled
            />
          )}

          {/* No environment for performance */}
          
          {/* LOD NanoCore Model */}
          <LODNanoCoreModel
            customization={customization}
            onLoad={handleLoad}
            onError={onError}
          />
          
          {/* Conditional shadows */}
          {adaptiveSettings.quality !== 'low' && (
            <ContactShadows 
              position={[0, -0.8, 0]} 
              opacity={0.4} 
              scale={3} 
              blur={2} 
              far={2} 
            />
          )}
          
          {/* Camera controls */}
          <OrbitControls
            enablePan={false}
            enableZoom={true}
            enableRotate={true}
            autoRotate={adaptiveSettings.autoRotate}
            autoRotateSpeed={2}
            minDistance={1}
            maxDistance={10}
            minPolarAngle={Math.PI / 6}
            maxPolarAngle={Math.PI - Math.PI / 6}
          />
        </Suspense>
      </Canvas>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="w-6 h-6 animate-spin text-cyan-400" />
            <span className="text-cyan-400 font-medium">Optimizing NanoCore...</span>
          </div>
        </div>
      )}
      
      {/* Performance stats */}
      {showStats && <PerformanceStats fps={performanceStats.fps} frameTime={performanceStats.frameTime} />}
      
      {/* Performance toggle */}
      <button
        onClick={() => setShowStats(!showStats)}
        className="absolute bottom-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-2 text-xs text-white hover:bg-black/90 transition-colors"
      >
        Stats
      </button>
      
      {/* Quality indicator */}
      <div className="absolute top-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-2 text-xs text-white">
        Quality: {adaptiveSettings.quality}
      </div>
    </div>
  )
}

export default NanoCoreOptimized

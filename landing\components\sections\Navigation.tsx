"use client"

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { Button } from '@/components/ui/button'

interface NavigationProps {
  onLogin: () => void
}

export function Navigation({ onLogin }: NavigationProps) {
  return (
    <motion.nav
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="flex items-center justify-between p-6 bg-black/20 backdrop-blur-xl border-b border-gray-800/50"
    >
      <div className="flex items-center gap-3">
        <Image src="/logo.png" alt="NanoHero Logo" width={300} height={40} />
        <p className="text-xs text-gray-400">Learn • Play • Hack Smart</p>
      </div>         
      <div className="flex items-center gap-4">
        <Button variant="ghost" className="text-gray-300 hover:text-white">
          Features
        </Button>
        <Button variant="ghost" className="text-gray-300 hover:text-white">
          Community
        </Button>
        <Button variant="ghost" className="text-gray-300 hover:text-white">
          Safety
        </Button>
        <Button variant="outline" className="border-gray-700 hover:bg-gray-800 bg-transparent" onClick={onLogin}>
          Sign In
        </Button>
      </div>
    </motion.nav>
  )
}

"use client"

import React, { useState, useCallback } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  User, 
  <PERSON><PERSON><PERSON>,
  RotateCcw,
  Save,
  ExternalLink,
  Sliders
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AvatarCustomization, AvatarPreset, DEFAULT_AVATAR_CUSTOMIZATION } from '@/types/avatar'

interface AvatarCustomizerProps {
  customization: AvatarCustomization
  onCustomizationChange: (updates: Partial<AvatarCustomization>) => void
  onSavePreset?: (name: string, description: string) => void
  onLoadPreset?: (preset: AvatarPreset) => void
  availablePresets?: AvatarPreset[]
  className?: string
}

// Color palette for customization
const SKIN_TONES = [
  '#FDBCB4', '#F1C27D', '#E0AC69', '#C68642', '#8D5524', '#654321'
]

const HAIR_COLORS = [
  '#000000', '#8B4513', '#D2691E', '#FFD700', '#FF6347', '#9400D3', '#00CED1'
]

const EYE_COLORS = [
  '#4A90E2', '#50C878', '#8B4513', '#32CD32', '#9400D3', '#FF1493'
]

const OUTFIT_OPTIONS = [
  { id: 'casual', name: 'Casual', description: 'Everyday wear' },
  { id: 'cyberpunk', name: 'Cyberpunk', description: 'Futuristic tech style' },
  { id: 'formal', name: 'Formal', description: 'Professional attire' },
  { id: 'gaming', name: 'Gaming', description: 'Gamer aesthetic' },
  { id: 'hero', name: 'Hero', description: 'Superhero costume' }
]

const HAIR_STYLES = [
  { id: 'short', name: 'Short' },
  { id: 'medium', name: 'Medium' },
  { id: 'long', name: 'Long' },
  { id: 'curly', name: 'Curly' },
  { id: 'spiky', name: 'Spiky' },
  { id: 'bald', name: 'Bald' }
]

export function AvatarCustomizer({
  customization,
  onCustomizationChange,
  onSavePreset,
  onLoadPreset,
  availablePresets = [],
  className = ""
}: AvatarCustomizerProps) {
  const [activeTab, setActiveTab] = useState("appearance")
  const [presetName, setPresetName] = useState("")
  const [showSavePreset, setShowSavePreset] = useState(false)

  const updateCustomization = useCallback((updates: Partial<AvatarCustomization>) => {
    onCustomizationChange(updates)
  }, [onCustomizationChange])

  const resetToDefault = () => {
    onCustomizationChange(DEFAULT_AVATAR_CUSTOMIZATION)
  }

  const openReadyPlayerMe = () => {
    // Open Ready Player Me in a new window
    const rpm = window.open(
      'https://nanohero.readyplayer.me/avatar?frameApi',
      'readyplayerme',
      'width=800,height=600'
    )

    // Listen for avatar creation
    window.addEventListener('message', (event) => {
      if (event.data?.source === 'readyplayerme') {
        if (event.data.eventName === 'v1.avatar.exported') {
          updateCustomization({
            readyPlayerMeUrl: event.data.data.url,
            readyPlayerMeId: event.data.data.id
          })
          rpm?.close()
        }
      }
    })
  }

  const handleSavePreset = () => {
    if (presetName.trim() && onSavePreset) {
      onSavePreset(presetName.trim(), `Custom preset: ${presetName}`)
      setPresetName("")
      setShowSavePreset(false)
    }
  }

  return (
    <Card className={`bg-black/40 border-gray-800/50 backdrop-blur-xl ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-cyan-400">
          <User className="w-5 h-5" />
          Avatar Customization
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="appearance" className="text-xs">
              <Palette className="w-4 h-4 mr-1" />
              Appearance
            </TabsTrigger>
            <TabsTrigger value="clothing" className="text-xs">
              <Shirt className="w-4 h-4 mr-1" />
              Clothing
            </TabsTrigger>
            <TabsTrigger value="presets" className="text-xs">
              <Sparkles className="w-4 h-4 mr-1" />
              Presets
            </TabsTrigger>
            <TabsTrigger value="advanced" className="text-xs">
              <Sliders className="w-4 h-4 mr-1" />
              Advanced
            </TabsTrigger>
          </TabsList>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-4">
            {/* Skin Tone */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Skin Tone</Label>
              <div className="flex gap-2 flex-wrap">
                {SKIN_TONES.map((color) => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 transition-all ${
                      customization.skinTone === color 
                        ? 'border-cyan-400 scale-110' 
                        : 'border-gray-600 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => updateCustomization({ skinTone: color })}
                  />
                ))}
              </div>
            </div>

            {/* Hair Style */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Hair Style</Label>
              <Select 
                value={customization.hairStyle} 
                onValueChange={(value) => updateCustomization({ hairStyle: value })}
              >
                <SelectTrigger className="bg-gray-800/50 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {HAIR_STYLES.map((style) => (
                    <SelectItem key={style.id} value={style.id}>
                      {style.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Hair Color */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Hair Color</Label>
              <div className="flex gap-2 flex-wrap">
                {HAIR_COLORS.map((color) => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 transition-all ${
                      customization.hairColor === color 
                        ? 'border-cyan-400 scale-110' 
                        : 'border-gray-600 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => updateCustomization({ hairColor: color })}
                  />
                ))}
              </div>
            </div>

            {/* Eye Color */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Eye Color</Label>
              <div className="flex gap-2 flex-wrap">
                {EYE_COLORS.map((color) => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 transition-all ${
                      customization.eyeColor === color 
                        ? 'border-cyan-400 scale-110' 
                        : 'border-gray-600 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => updateCustomization({ eyeColor: color })}
                  />
                ))}
              </div>
            </div>

            {/* Height */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">
                Height: {Math.round(customization.height * 100)}%
              </Label>
              <Slider
                value={[customization.height]}
                onValueChange={([value]) => updateCustomization({ height: value })}
                min={0.8}
                max={1.2}
                step={0.05}
                className="w-full"
              />
            </div>
          </TabsContent>

          {/* Clothing Tab */}
          <TabsContent value="clothing" className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Outfit Style</Label>
              <div className="grid grid-cols-1 gap-2">
                {OUTFIT_OPTIONS.map((outfit) => (
                  <button
                    key={outfit.id}
                    className={`p-3 rounded-lg border text-left transition-all ${
                      customization.outfit === outfit.id
                        ? 'border-cyan-400 bg-cyan-400/10'
                        : 'border-gray-600 bg-gray-800/30 hover:border-gray-400'
                    }`}
                    onClick={() => updateCustomization({ outfit: outfit.id })}
                  >
                    <div className="font-medium text-white">{outfit.name}</div>
                    <div className="text-sm text-gray-400">{outfit.description}</div>
                  </button>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Presets Tab */}
          <TabsContent value="presets" className="space-y-4">
            {/* Ready Player Me Integration */}
            <div className="p-4 bg-gradient-to-r from-purple-900/30 to-blue-900/30 rounded-lg border border-purple-500/30">
              <h3 className="font-semibold text-white mb-2 flex items-center gap-2">
                <ExternalLink className="w-4 h-4" />
                Ready Player Me
              </h3>
              <p className="text-sm text-gray-300 mb-3">
                Create a professional 3D avatar with advanced customization options
              </p>
              <Button 
                onClick={openReadyPlayerMe}
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open Ready Player Me
              </Button>
            </div>

            {/* Save Current as Preset */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Save Current Avatar</Label>
              {!showSavePreset ? (
                <Button 
                  variant="outline" 
                  onClick={() => setShowSavePreset(true)}
                  className="w-full border-gray-600 hover:bg-gray-800"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save as Preset
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Input
                    placeholder="Preset name..."
                    value={presetName}
                    onChange={(e) => setPresetName(e.target.value)}
                    className="bg-gray-800/50 border-gray-600"
                  />
                  <Button onClick={handleSavePreset} size="sm">Save</Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowSavePreset(false)} 
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>

            {/* Available Presets */}
            {availablePresets.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-300">Available Presets</Label>
                <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                  {availablePresets.map((preset) => (
                    <button
                      key={preset.id}
                      className="p-2 rounded-lg border border-gray-600 bg-gray-800/30 hover:border-gray-400 text-left"
                      onClick={() => onLoadPreset?.(preset)}
                    >
                      <div className="font-medium text-white text-sm">{preset.name}</div>
                      <div className="text-xs text-gray-400">{preset.description}</div>
                      <Badge variant="outline" className="mt-1 text-xs">
                        {preset.category}
                      </Badge>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          {/* Advanced Tab */}
          <TabsContent value="advanced" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-300">Animation Preferences</Label>
                <Select 
                  value={customization.idleAnimation} 
                  onValueChange={(value) => updateCustomization({ idleAnimation: value })}
                >
                  <SelectTrigger className="bg-gray-800/50 border-gray-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="breathing">Breathing</SelectItem>
                    <SelectItem value="looking_around">Looking Around</SelectItem>
                    <SelectItem value="subtle_movement">Subtle Movement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-6">
          <Button 
            variant="outline" 
            onClick={resetToDefault}
            className="flex-1 border-gray-600 hover:bg-gray-800"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

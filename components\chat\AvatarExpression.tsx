"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  <PERSON>, 
  Brain, 
  Eye, 
  Heart, 
  Minus,
  ChevronDown,
  Sparkles
} from 'lucide-react'

type AvatarExpression = 'happy' | 'focused' | 'curious' | 'helpful' | 'neutral'

interface AvatarExpressionProps {
  currentExpression: AvatarExpression
  onExpressionChange: (expression: AvatarExpression) => void
  avatarUrl?: string
  userName: string
  showPicker?: boolean
  className?: string
}

const expressions = {
  happy: {
    icon: Smile,
    label: 'Happy',
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    borderColor: 'border-yellow-500/30',
    effect: '😊',
    description: 'Feeling joyful and positive'
  },
  focused: {
    icon: Brain,
    label: 'Focused',
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    borderColor: 'border-blue-500/30',
    effect: '🧠',
    description: 'Deep in thought and concentration'
  },
  curious: {
    icon: Eye,
    label: 'Curious',
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20',
    borderColor: 'border-purple-500/30',
    effect: '🤔',
    description: 'Eager to learn and explore'
  },
  helpful: {
    icon: Heart,
    label: 'Helpful',
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    borderColor: 'border-green-500/30',
    effect: '🤝',
    description: 'Ready to assist others'
  },
  neutral: {
    icon: Minus,
    label: 'Neutral',
    color: 'text-gray-400',
    bgColor: 'bg-gray-500/20',
    borderColor: 'border-gray-500/30',
    effect: '😐',
    description: 'Calm and balanced'
  }
}

export function AvatarExpression({
  currentExpression,
  onExpressionChange,
  avatarUrl,
  userName,
  showPicker = false,
  className = ""
}: AvatarExpressionProps) {
  const [isPickerOpen, setIsPickerOpen] = useState(false)
  const currentConfig = expressions[currentExpression]
  const _CurrentIcon = currentConfig.icon

  const handleExpressionSelect = (expression: AvatarExpression) => {
    onExpressionChange(expression)
    setIsPickerOpen(false)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Avatar with Expression Overlay */}
      <div className="relative">
        <Avatar className="w-10 h-10">
          <AvatarImage src={avatarUrl} />
          <AvatarFallback className="bg-gradient-to-r from-cyan-500 to-purple-500 text-white">
            {userName.charAt(0)}
          </AvatarFallback>
        </Avatar>

        {/* Expression Indicator */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full ${currentConfig.bgColor} ${currentConfig.borderColor} border-2 flex items-center justify-center`}
        >
          <span className="text-xs">{currentConfig.effect}</span>
        </motion.div>

        {/* Quantum Glow Effect */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className={`absolute inset-0 rounded-full ${currentConfig.bgColor} blur-sm -z-10`}
        />

        {/* Expression Picker Button */}
        {showPicker && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPickerOpen(!isPickerOpen)}
            className="absolute -top-2 -right-2 w-6 h-6 p-0 bg-gray-800/80 hover:bg-gray-700/80 border border-gray-600"
          >
            <ChevronDown className="w-3 h-3 text-gray-400" />
          </Button>
        )}
      </div>

      {/* Expression Picker Dropdown */}
      <AnimatePresence>
        {isPickerOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -10 }}
            className="absolute top-full left-0 mt-2 bg-black/95 backdrop-blur-xl border border-gray-800/50 rounded-lg p-3 z-50 min-w-[200px]"
          >
            <div className="space-y-2">
              <div className="text-xs text-gray-400 mb-3 flex items-center gap-2">
                <Sparkles className="w-3 h-3" />
                Choose your expression
              </div>
              
              {Object.entries(expressions).map(([key, config]) => {
                const Icon = config.icon
                const isSelected = currentExpression === key
                
                return (
                  <motion.button
                    key={key}
                    onClick={() => handleExpressionSelect(key as AvatarExpression)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full flex items-center gap-3 p-2 rounded-lg transition-all ${
                      isSelected
                        ? `${config.bgColor} ${config.borderColor} border text-white`
                        : 'hover:bg-gray-800/50 text-gray-300'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full ${config.bgColor} ${config.borderColor} border flex items-center justify-center`}>
                      <Icon className={`w-4 h-4 ${config.color}`} />
                    </div>
                    <div className="flex-1 text-left">
                      <div className="text-sm font-medium">{config.label}</div>
                      <div className="text-xs text-gray-400">{config.description}</div>
                    </div>
                    <span className="text-lg">{config.effect}</span>
                  </motion.button>
                )
              })}
            </div>

            <div className="mt-3 pt-3 border-t border-gray-700">
              <p className="text-xs text-gray-500 text-center">
                Your expression shows in chat messages
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Compact version for message display
interface MessageAvatarExpressionProps {
  expression: AvatarExpression
  avatarUrl?: string
  userName: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function MessageAvatarExpression({
  expression,
  avatarUrl,
  userName,
  size = 'md',
  className = ""
}: MessageAvatarExpressionProps) {
  const config = expressions[expression]
  const sizes = {
    sm: { avatar: 'w-6 h-6', indicator: 'w-4 h-4', text: 'text-xs' },
    md: { avatar: 'w-8 h-8', indicator: 'w-5 h-5', text: 'text-xs' },
    lg: { avatar: 'w-10 h-10', indicator: 'w-6 h-6', text: 'text-sm' }
  }
  const sizeConfig = sizes[size]

  return (
    <div className={`relative ${className}`}>
      <Avatar className={sizeConfig.avatar}>
        <AvatarImage src={avatarUrl} />
        <AvatarFallback className="bg-gradient-to-r from-cyan-500 to-purple-500 text-white text-xs">
          {userName.charAt(0)}
        </AvatarFallback>
      </Avatar>

      {/* Expression Indicator */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className={`absolute -bottom-0.5 -right-0.5 ${sizeConfig.indicator} rounded-full ${config.bgColor} ${config.borderColor} border flex items-center justify-center`}
      >
        <span className={sizeConfig.text}>{config.effect}</span>
      </motion.div>

      {/* Subtle Glow */}
      <div className={`absolute inset-0 rounded-full ${config.bgColor} opacity-20 blur-sm -z-10`} />
    </div>
  )
}

// Expression tooltip for hover states
export function ExpressionTooltip({ expression }: { expression: AvatarExpression }) {
  const config = expressions[expression]
  
  return (
    <div className="bg-black/90 backdrop-blur-sm border border-gray-700 rounded-lg p-2 text-xs">
      <div className="flex items-center gap-2">
        <span>{config.effect}</span>
        <span className="text-white font-medium">{config.label}</span>
      </div>
      <div className="text-gray-400 mt-1">{config.description}</div>
    </div>
  )
}

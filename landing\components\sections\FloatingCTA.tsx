"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Rocket, X } from 'lucide-react'

interface FloatingCTAProps {
  showFloatingCTA: boolean
  onSignupClick: () => void
}

export function FloatingCTA({ showFloatingCTA, onSignupClick }: FloatingCTAProps) {
  const [isVisible, setIsVisible] = React.useState(true)

  if (!isVisible || !showFloatingCTA) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100, scale: 0.8 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.8 }}
        className="fixed bottom-6 right-6 z-50 max-w-sm"
      >
        <motion.div
          whileHover={{ scale: 1.02 }}
          className="bg-gradient-to-r from-purple-500 to-cyan-500 rounded-xl p-6 shadow-2xl border border-purple-400/20 backdrop-blur-xl"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center"
              >
                <Rocket className="w-5 h-5 text-white" />
              </motion.div>
              <div>
                <h4 className="text-white font-bold">Ready to Start?</h4>
                <p className="text-white/80 text-sm">Join the adventure!</p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsVisible(false)}
              className="text-white/70 hover:text-white"
            >
              <X className="w-5 h-5" />
            </motion.button>
          </div>
          
          <p className="text-white/90 text-sm mb-4">
            Join thousands of young learners building their tech future!
          </p>
          
          <div className="flex gap-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex-1"
            >
              <Button
                onClick={onSignupClick}
                className="w-full bg-white text-purple-600 hover:bg-gray-100 font-semibold"
              >
                Start Learning
              </Button>
            </motion.div>
          </div>
          
          <div className="flex items-center justify-center gap-4 mt-3 text-xs text-white/70">
            <span>✓ Free to start</span>
            <span>✓ Safe & secure</span>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

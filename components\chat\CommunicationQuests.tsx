"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Target, 
  Trophy, 
  Clock, 
  Coins, 
  Zap, 
  Star,
  CheckCircle,
  HelpCircle,
  Heart,
  MessageCircle,
  Users,
  Sparkles,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { CommunicationQuest } from '@/types/chat'
import { isQuestComplete } from '@/data/communicationQuests'

interface CommunicationQuestsProps {
  quests: CommunicationQuest[]
  onClaimReward: (questId: string) => void
  className?: string
}

const questTypeConfig = {
  help_someone: {
    icon: HelpCircle,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/30'
  },
  give_compliment: {
    icon: Heart,
    color: 'from-pink-500 to-rose-500',
    bgColor: 'bg-pink-500/10',
    borderColor: 'border-pink-500/30'
  },
  explain_concept: {
    icon: MessageCircle,
    color: 'from-blue-500 to-indigo-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/30'
  },
  show_kindness: {
    icon: Sparkles,
    color: 'from-yellow-500 to-orange-500',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/30'
  },
  resolve_conflict: {
    icon: Users,
    color: 'from-purple-500 to-indigo-500',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/30'
  }
}

export function CommunicationQuests({
  quests,
  onClaimReward,
  className = ""
}: CommunicationQuestsProps) {
  const [expandedQuest, setExpandedQuest] = useState<string | null>(null)

  const activeQuests = quests.filter(quest => !isQuestComplete(quest))
  const completedQuests = quests.filter(quest => isQuestComplete(quest))

  const toggleQuestExpansion = (questId: string) => {
    setExpandedQuest(expandedQuest === questId ? null : questId)
  }

  const formatTimeRemaining = (timeLimit?: Date) => {
    if (!timeLimit) return null
    
    const now = new Date()
    const remaining = timeLimit.getTime() - now.getTime()
    
    if (remaining <= 0) return 'Expired'
    
    const hours = Math.floor(remaining / (1000 * 60 * 60))
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}d ${hours % 24}h`
    }
    
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
  }

  const QuestCard = ({ quest, isCompleted }: { quest: CommunicationQuest, isCompleted: boolean }) => {
    const config = questTypeConfig[quest.type]
    const Icon = config.icon
    const progressPercentage = Math.min(100, (quest.progress / quest.target) * 100)
    const isExpanded = expandedQuest === quest.id

    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`border rounded-lg overflow-hidden transition-all ${
          isCompleted 
            ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-500/30' 
            : `${config.bgColor} ${config.borderColor}`
        }`}
      >
        <div 
          className="p-4 cursor-pointer"
          onClick={() => toggleQuestExpansion(quest.id)}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1">
              <div className={`w-10 h-10 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center flex-shrink-0`}>
                {isCompleted ? (
                  <CheckCircle className="w-5 h-5 text-white" />
                ) : (
                  <Icon className="w-5 h-5 text-white" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-white truncate">
                    {quest.title}
                  </h4>
                  {quest.isDaily && (
                    <Badge variant="secondary" className="text-xs">
                      Daily
                    </Badge>
                  )}
                  {quest.timeLimit && (
                    <Badge variant="outline" className="text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimeRemaining(quest.timeLimit)}
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-gray-400 mb-3">
                  {quest.description}
                </p>
                
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-300">
                      Progress: {quest.progress}/{quest.target}
                    </span>
                    <span className="text-gray-400">
                      {Math.round(progressPercentage)}%
                    </span>
                  </div>
                  <Progress 
                    value={progressPercentage} 
                    className="h-2"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 ml-4">
              {isCompleted && (
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onClaimReward(quest.id)
                  }}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white"
                >
                  <Trophy className="w-4 h-4 mr-1" />
                  Claim
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Expanded Details */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-t border-gray-700/50"
            >
              <div className="p-4 space-y-4">
                {/* Rewards */}
                <div>
                  <h5 className="text-sm font-medium text-white mb-2">Rewards:</h5>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30">
                      <Zap className="w-3 h-3 mr-1" />
                      {quest.reward.ce} CE
                    </Badge>
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                      <Coins className="w-3 h-3 mr-1" />
                      {quest.reward.cubits} Cubits
                    </Badge>
                    {quest.reward.badge && (
                      <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                        <Star className="w-3 h-3 mr-1" />
                        {quest.reward.badge}
                      </Badge>
                    )}
                    {quest.reward.avatarUpgrade && (
                      <Badge className="bg-pink-500/20 text-pink-400 border-pink-500/30">
                        <Sparkles className="w-3 h-3 mr-1" />
                        Avatar Upgrade
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Tips */}
                <div className="p-3 bg-gray-800/50 rounded-lg">
                  <h5 className="text-sm font-medium text-white mb-2">💡 Tips:</h5>
                  <ul className="text-xs text-gray-400 space-y-1">
                    {quest.type === 'help_someone' && (
                      <>
                        <li>• Look for questions in chat and offer assistance</li>
                        <li>• Share your knowledge when others are stuck</li>
                        <li>• Use encouraging words when helping</li>
                      </>
                    )}
                    {quest.type === 'give_compliment' && (
                      <>
                        <li>• Celebrate others&apos; achievements and progress</li>
                        <li>• Notice when someone does something well</li>
                        <li>• Be genuine and specific in your compliments</li>
                      </>
                    )}
                    {quest.type === 'explain_concept' && (
                      <>
                        <li>• Break down complex ideas into simple steps</li>
                        <li>• Use examples to make concepts clearer</li>
                        <li>• Ask if your explanation makes sense</li>
                      </>
                    )}
                    {quest.type === 'show_kindness' && (
                      <>
                        <li>• Welcome new users to the community</li>
                        <li>• Thank others for their contributions</li>
                        <li>• Offer support when someone is struggling</li>
                      </>
                    )}
                    {quest.type === 'resolve_conflict' && (
                      <>
                        <li>• Listen to all sides of a disagreement</li>
                        <li>• Help find common ground between users</li>
                        <li>• Suggest peaceful solutions to problems</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    )
  }

  return (
    <div className={className}>
      <Card className="bg-black/40 backdrop-blur-xl border-gray-800/50">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-cyan-400" />
            Communication Quests
          </CardTitle>
          <p className="text-sm text-gray-400">
            Complete quests to earn rewards and improve your communication skills
          </p>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Active Quests */}
          {activeQuests.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <Clock className="w-4 h-4 text-yellow-400" />
                Active Quests ({activeQuests.length})
              </h3>
              <div className="space-y-3">
                {activeQuests.map(quest => (
                  <QuestCard key={quest.id} quest={quest} isCompleted={false} />
                ))}
              </div>
            </div>
          )}

          {/* Completed Quests */}
          {completedQuests.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Completed Quests ({completedQuests.length})
              </h3>
              <div className="space-y-3">
                {completedQuests.map(quest => (
                  <QuestCard key={quest.id} quest={quest} isCompleted={true} />
                ))}
              </div>
            </div>
          )}

          {/* No Quests */}
          {quests.length === 0 && (
            <div className="text-center py-8">
              <Target className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No active quests at the moment</p>
              <p className="text-sm text-gray-500 mt-2">
                New quests will appear as you participate in the community
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

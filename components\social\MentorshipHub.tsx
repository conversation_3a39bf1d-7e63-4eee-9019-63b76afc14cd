"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Star, 
  Clock, 
  Calendar, 
  MessageCircle,
  Video,
  Award,
  Search,
  Plus,
  TrendingUp,
  CheckCircle,
  User
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  MentorProfile, 
  MentorshipRequest, 
  MentorshipSession, 
  User as UserType 
} from '@/types/social'

interface MentorshipHubProps {
  currentUser: UserType
  mentors: MentorProfile[]
  requests: MentorshipRequest[]
  sessions: MentorshipSession[]
  onRequestMentorship: (mentorId: string, request: Partial<MentorshipRequest>) => void
  onAcceptRequest: (requestId: string) => void
  onScheduleSession: (sessionData: Partial<MentorshipSession>) => void
  onJoinSession: (sessionId: string) => void
  className?: string
}

export function MentorshipHub({
  currentUser,
  mentors,
  requests,
  sessions,
  onRequestMentorship: _onRequestMentorship,
  onAcceptRequest,
  onScheduleSession: _onScheduleSession,
  onJoinSession,
  className = ""
}: MentorshipHubProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSkill, setSelectedSkill] = useState<string | null>(null)
  const [_showRequestForm, setShowRequestForm] = useState(false)
  const [_selectedMentor, setSelectedMentor] = useState<MentorProfile | null>(null)

  const filteredMentors = mentors.filter(mentor => 
    mentor.isAvailable &&
    (searchQuery === '' || 
     mentor.expertise.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())) ||
     mentor.bio.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (selectedSkill === null || mentor.expertise.includes(selectedSkill))
  )

  const myRequests = requests.filter(req => req.menteeId === currentUser.id)
  const incomingRequests = requests.filter(req => 
    mentors.find(m => m.userId === currentUser.id) && req.status === 'open'
  )
  const upcomingSessions = sessions.filter(session => 
    (session.mentorId === currentUser.id || session.menteeId === currentUser.id) &&
    session.status === 'scheduled' &&
    session.scheduledAt > new Date()
  )

  const allSkills = Array.from(new Set(mentors.flatMap(m => m.expertise)))

  const renderMentorCard = (mentor: MentorProfile) => (
    <motion.div
      key={mentor.userId}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="group"
    >
      <Card className="bg-gray-800 border-gray-700 hover:border-cyan-400 transition-all cursor-pointer">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={`/api/avatar/${mentor.userId}`} />
              <AvatarFallback><User className="w-8 h-8" /></AvatarFallback>
            </Avatar>

            <div className="flex-1 space-y-3">
              <div>
                <h3 className="font-semibold text-white text-lg">Mentor Profile</h3>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-300">{mentor.rating.toFixed(1)}</span>
                  </div>
                  <span className="text-sm text-gray-400">•</span>
                  <span className="text-sm text-gray-400">{mentor.reviewCount} reviews</span>
                  <span className="text-sm text-gray-400">•</span>
                  <span className="text-sm text-gray-400">{mentor.successStories} success stories</span>
                </div>
              </div>

              <p className="text-sm text-gray-300 line-clamp-2">{mentor.bio}</p>

              <div className="space-y-2">
                <div>
                  <span className="text-xs text-gray-400 uppercase tracking-wide">Expertise</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {mentor.expertise.slice(0, 4).map((skill) => (
                      <Badge key={skill} variant="outline" className="text-xs border-cyan-400 text-cyan-400">
                        {skill}
                      </Badge>
                    ))}
                    {mentor.expertise.length > 4 && (
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                        +{mentor.expertise.length - 4} more
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{mentor.currentMentees}/{mentor.maxMentees} mentees</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Award className="w-4 h-4" />
                      <span>{mentor.achievements.length} achievements</span>
                    </div>
                  </div>

                  <Button
                    onClick={() => {
                      setSelectedMentor(mentor)
                      setShowRequestForm(true)
                    }}
                    className="bg-cyan-600 hover:bg-cyan-700"
                    disabled={mentor.currentMentees >= mentor.maxMentees}
                  >
                    Request Mentorship
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )

  const renderRequestCard = (request: MentorshipRequest) => (
    <Card key={request.id} className="bg-gray-800 border-gray-700">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h4 className="font-medium text-white">{request.subject}</h4>
            <p className="text-sm text-gray-300">{request.description}</p>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span className="capitalize">{request.timeCommitment}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span className="capitalize">{request.duration}</span>
              </div>
            </div>
            <div className="flex flex-wrap gap-1">
              {request.skillsNeeded.map((skill) => (
                <Badge key={skill} variant="outline" className="text-xs">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <Badge 
              variant={request.status === 'open' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {request.status}
            </Badge>
            {request.status === 'open' && incomingRequests.includes(request) && (
              <Button
                size="sm"
                onClick={() => onAcceptRequest(request.id)}
                className="bg-green-600 hover:bg-green-700"
              >
                Accept
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderSessionCard = (session: MentorshipSession) => {
    const isUpcoming = session.scheduledAt > new Date()
    const canJoin = isUpcoming && 
      new Date(session.scheduledAt.getTime() - 15 * 60 * 1000) <= new Date()

    return (
      <Card key={session.id} className="bg-gray-800 border-gray-700">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h4 className="font-medium text-white">{session.title}</h4>
              {session.description && (
                <p className="text-sm text-gray-300">{session.description}</p>
              )}
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{session.scheduledAt.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{session.scheduledAt.toLocaleTimeString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Video className="w-4 h-4" />
                  <span>{session.duration} min</span>
                </div>
              </div>
              {session.goals.length > 0 && (
                <div>
                  <span className="text-xs text-gray-400 uppercase tracking-wide">Goals</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {session.goals.map((goal, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {goal}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2">
              <Badge 
                variant={session.status === 'scheduled' ? 'default' : 'secondary'}
                className="capitalize"
              >
                {session.status}
              </Badge>
              {canJoin && (
                <Button
                  size="sm"
                  onClick={() => onJoinSession(session.id)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Join Session
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Mentorship Hub</h1>
          <p className="text-gray-400 mt-1">Connect with mentors and grow your skills</p>
        </div>
        <Button
          onClick={() => setShowRequestForm(true)}
          className="bg-cyan-600 hover:bg-cyan-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Request Mentorship
        </Button>
      </div>

      <Tabs defaultValue="find-mentors" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-gray-800">
          <TabsTrigger value="find-mentors">Find Mentors</TabsTrigger>
          <TabsTrigger value="my-requests">My Requests ({myRequests.length})</TabsTrigger>
          <TabsTrigger value="sessions">Sessions ({upcomingSessions.length})</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="find-mentors" className="space-y-6">
          {/* Search and Filters */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="flex gap-4">
                <div className="flex-1 relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search mentors by expertise or bio..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={selectedSkill ? "default" : "outline"}
                    onClick={() => setSelectedSkill(null)}
                    className="whitespace-nowrap"
                  >
                    All Skills
                  </Button>
                  {allSkills.slice(0, 4).map((skill) => (
                    <Button
                      key={skill}
                      variant={selectedSkill === skill ? "default" : "outline"}
                      onClick={() => setSelectedSkill(selectedSkill === skill ? null : skill)}
                      className="whitespace-nowrap"
                    >
                      {skill}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Mentors Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredMentors.map(renderMentorCard)}
          </div>

          {filteredMentors.length === 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No mentors found</h3>
                <p className="text-gray-400">Try adjusting your search criteria</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="my-requests" className="space-y-4">
          {myRequests.length > 0 ? (
            myRequests.map(renderRequestCard)
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No mentorship requests</h3>
                <p className="text-gray-400">Start by requesting mentorship from available mentors</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          {upcomingSessions.length > 0 ? (
            upcomingSessions.map(renderSessionCard)
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No upcoming sessions</h3>
                <p className="text-gray-400">Schedule sessions with your mentors or mentees</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="dashboard" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-cyan-600 rounded-lg">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{myRequests.length}</p>
                    <p className="text-sm text-gray-400">Active Requests</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-green-600 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">
                      {sessions.filter(s => s.status === 'completed').length}
                    </p>
                    <p className="text-sm text-gray-400">Completed Sessions</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-purple-600 rounded-lg">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">85%</p>
                    <p className="text-sm text-gray-400">Success Rate</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-gray-700 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <div>
                    <p className="text-sm text-white">Completed session with mentor</p>
                    <p className="text-xs text-gray-400">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-700 rounded-lg">
                  <MessageCircle className="w-5 h-5 text-cyan-400" />
                  <div>
                    <p className="text-sm text-white">New mentorship request received</p>
                    <p className="text-xs text-gray-400">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default MentorshipHub

"use client"

import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Star, 
  Heart, 
  Share2, 
  Download,
  Search,
  Grid3X3,
  List,
  Trophy,
  Crown,
  Sparkles,
  Eye
} from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { NanoCore } from './NanoCore'
import { 
  NanoCoreCustomization, 
  DEFAULT_AVATAR_SETTINGS,
  DEFAULT_NANOCORE_CUSTOMIZATION 
} from '@/types/avatar'

interface NanoCoreShowcase {
  id: string
  name: string
  description: string
  customization: NanoCoreCustomization
  creator: {
    name: string
    avatar: string
    level: number
  }
  stats: {
    likes: number
    downloads: number
    views: number
  }
  tags: string[]
  featured: boolean
  createdAt: Date
}

interface NanoCoreGalleryProps {
  showcases?: NanoCoreShowcase[]
  onLike?: (id: string) => void
  onDownload?: (id: string) => void
  onShare?: (id: string) => void
  onView?: (showcase: NanoCoreShowcase) => void
  className?: string
}

// Sample showcase data
const sampleShowcases: NanoCoreShowcase[] = [
  {
    id: '1',
    name: 'Quantum Guardian',
    description: 'A powerful quantum-enhanced NanoCore with advanced particle effects',
    customization: {
      ...DEFAULT_NANOCORE_CUSTOMIZATION,
      coreType: 'crystal',
      coreColor: '#9400D3',
      coreGlow: 1.0,
      energyCore: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
        type: 'quantum',
        color: '#9400D3',
        intensity: 0.9,
        particles: true
      },
      evolutionStage: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
        stage: 'hero',
        level: 50
      }
    },
    creator: { name: 'QuantumMaster', avatar: '🚀', level: 45 },
    stats: { likes: 1247, downloads: 892, views: 5634 },
    tags: ['quantum', 'hero', 'purple', 'advanced'],
    featured: true,
    createdAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'Cyber Sage',
    description: 'Wise mentor-level NanoCore with holographic wisdom aura',
    customization: {
      ...DEFAULT_NANOCORE_CUSTOMIZATION,
      coreType: 'organic',
      coreColor: '#00ffff',
      headUnit: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
        type: 'neural',
        size: 1.2
      },
      evolutionStage: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
        stage: 'mentor',
        level: 35
      }
    },
    creator: { name: 'WisdomSeeker', avatar: '🧠', level: 38 },
    stats: { likes: 856, downloads: 623, views: 3421 },
    tags: ['mentor', 'wisdom', 'cyan', 'neural'],
    featured: false,
    createdAt: new Date('2024-01-20')
  },
  {
    id: '3',
    name: 'Fire Phoenix',
    description: 'Blazing hero-stage NanoCore with flame-inspired design',
    customization: {
      ...DEFAULT_NANOCORE_CUSTOMIZATION,
      coreType: 'geometric',
      coreColor: '#ff6600',
      coreGlow: 1.0,
      energyCore: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
        type: 'vortex',
        color: '#ff6600',
        intensity: 1.0,
        particles: true
      },
      evolutionStage: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
        stage: 'hero',
        level: 48
      }
    },
    creator: { name: 'FlameForge', avatar: '🔥', level: 42 },
    stats: { likes: 1089, downloads: 734, views: 4567 },
    tags: ['hero', 'fire', 'orange', 'geometric'],
    featured: true,
    createdAt: new Date('2024-01-25')
  }
]

export function NanoCoreGallery({
  showcases = sampleShowcases,
  onLike,
  onDownload,
  onShare,
  onView,
  className = ""
}: NanoCoreGalleryProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStage, setFilterStage] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('likes')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedShowcase, setSelectedShowcase] = useState<NanoCoreShowcase | null>(null)
  
  // Filter and sort showcases
  const filteredShowcases = useMemo(() => {
    let filtered = showcases.filter(showcase => {
      const matchesSearch = showcase.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           showcase.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           showcase.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesStage = filterStage === 'all' || showcase.customization.evolutionStage.stage === filterStage
      
      return matchesSearch && matchesStage
    })
    
    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'likes':
          return b.stats.likes - a.stats.likes
        case 'downloads':
          return b.stats.downloads - a.stats.downloads
        case 'views':
          return b.stats.views - a.stats.views
        case 'newest':
          return b.createdAt.getTime() - a.createdAt.getTime()
        default:
          return 0
      }
    })
    
    return filtered
  }, [showcases, searchTerm, filterStage, sortBy])
  
  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'hero': return <Crown className="w-4 h-4" />
      case 'mentor': return <Trophy className="w-4 h-4" />
      case 'helper': return <Sparkles className="w-4 h-4" />
      default: return <Star className="w-4 h-4" />
    }
  }
  
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'hero': return 'text-orange-400 border-orange-400'
      case 'mentor': return 'text-purple-400 border-purple-400'
      case 'helper': return 'text-yellow-400 border-yellow-400'
      default: return 'text-cyan-400 border-cyan-400'
    }
  }
  
  const handleShowcaseClick = (showcase: NanoCoreShowcase) => {
    setSelectedShowcase(showcase)
    onView?.(showcase)
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-cyan-400">NanoCore Gallery</h2>
          <p className="text-gray-400">Discover and share amazing NanoCore creations</p>
        </div>
        
        {/* Controls */}
        <div className="flex flex-wrap gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search showcases..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-800 border-gray-600 w-64"
            />
          </div>
          
          <Select value={filterStage} onValueChange={setFilterStage}>
            <SelectTrigger className="w-32 bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="all">All Stages</SelectItem>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="initiate">Initiate</SelectItem>
              <SelectItem value="helper">Helper</SelectItem>
              <SelectItem value="mentor">Mentor</SelectItem>
              <SelectItem value="hero">Hero</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32 bg-gray-800 border-gray-600">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              <SelectItem value="likes">Most Liked</SelectItem>
              <SelectItem value="downloads">Most Downloaded</SelectItem>
              <SelectItem value="views">Most Viewed</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex border border-gray-600 rounded-lg overflow-hidden">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-none"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Featured Showcases */}
      {filteredShowcases.some(s => s.featured) && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-cyan-400">Featured Creations</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredShowcases.filter(s => s.featured).map((showcase) => (
              <motion.div
                key={showcase.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.02 }}
                className="cursor-pointer"
                onClick={() => handleShowcaseClick(showcase)}
              >
                <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-cyan-400/30 hover:border-cyan-400 transition-all">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="border-yellow-400 text-yellow-400">
                        Featured
                      </Badge>
                      <Badge variant="outline" className={getStageColor(showcase.customization.evolutionStage.stage)}>
                        {getStageIcon(showcase.customization.evolutionStage.stage)}
                        <span className="ml-1 capitalize">{showcase.customization.evolutionStage.stage}</span>
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* 3D Preview */}
                    <div className="h-48 bg-gray-800 rounded-lg overflow-hidden">
                      <NanoCore
                        customization={showcase.customization}
                        settings={{ ...DEFAULT_AVATAR_SETTINGS, autoRotate: true }}
                        interactionMode="showcase"
                      />
                    </div>
                    
                    {/* Info */}
                    <div className="space-y-2">
                      <h4 className="font-bold text-white">{showcase.name}</h4>
                      <p className="text-sm text-gray-400 line-clamp-2">{showcase.description}</p>
                      
                      {/* Creator */}
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-lg">{showcase.creator.avatar}</span>
                        <span className="text-gray-300">{showcase.creator.name}</span>
                        <Badge variant="outline" className="text-xs">
                          Lv.{showcase.creator.level}
                        </Badge>
                      </div>
                      
                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-gray-400">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Heart className="w-4 h-4" />
                            {showcase.stats.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="w-4 h-4" />
                            {showcase.stats.downloads}
                          </span>
                          <span className="flex items-center gap-1">
                            <Eye className="w-4 h-4" />
                            {showcase.stats.views}
                          </span>
                        </div>
                      </div>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-1">
                        {showcase.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}
      
      {/* All Showcases */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-cyan-400">
          All Creations ({filteredShowcases.length})
        </h3>
        
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
          : "space-y-4"
        }>
          {filteredShowcases.map((showcase) => (
            <motion.div
              key={showcase.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="cursor-pointer"
              onClick={() => handleShowcaseClick(showcase)}
            >
              <Card className="bg-gray-900/95 border-gray-700 hover:border-cyan-400 transition-all">
                <CardContent className={viewMode === 'grid' ? "p-4 space-y-3" : "p-4"}>
                  <div className={viewMode === 'list' ? "flex gap-4" : ""}>
                    {/* 3D Preview */}
                    <div className={viewMode === 'grid' ? "h-32" : "w-32 h-32 flex-shrink-0"}>
                      <div className="w-full h-full bg-gray-800 rounded-lg overflow-hidden">
                        <NanoCore
                          customization={showcase.customization}
                          settings={{ ...DEFAULT_AVATAR_SETTINGS, autoRotate: true }}
                          interactionMode="showcase"
                        />
                      </div>
                    </div>
                    
                    {/* Content */}
                    <div className={viewMode === 'list' ? "flex-1 space-y-2" : "space-y-2"}>
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-white text-sm">{showcase.name}</h4>
                        <Badge variant="outline" className={`text-xs ${getStageColor(showcase.customization.evolutionStage.stage)}`}>
                          {getStageIcon(showcase.customization.evolutionStage.stage)}
                        </Badge>
                      </div>
                      
                      {viewMode === 'list' && (
                        <p className="text-sm text-gray-400">{showcase.description}</p>
                      )}
                      
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>{showcase.creator.name}</span>
                        <div className="flex gap-2">
                          <span>❤️ {showcase.stats.likes}</span>
                          <span>⬇️ {showcase.stats.downloads}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
      
      {/* Detailed View Modal */}
      <AnimatePresence>
        {selectedShowcase && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedShowcase(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-lg border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-cyan-400">{selectedShowcase.name}</h2>
                  <Button variant="outline" onClick={() => setSelectedShowcase(null)}>
                    Close
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="h-96">
                    <NanoCore
                      customization={selectedShowcase.customization}
                      settings={DEFAULT_AVATAR_SETTINGS}
                      interactionMode="showcase"
                    />
                  </div>
                  
                  <div className="space-y-4">
                    <p className="text-gray-300">{selectedShowcase.description}</p>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onLike?.(selectedShowcase.id)}
                        className="flex items-center gap-2"
                      >
                        <Heart className="w-4 h-4" />
                        Like ({selectedShowcase.stats.likes})
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDownload?.(selectedShowcase.id)}
                        className="flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        Download
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onShare?.(selectedShowcase.id)}
                        className="flex items-center gap-2"
                      >
                        <Share2 className="w-4 h-4" />
                        Share
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold text-white">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedShowcase.tags.map((tag) => (
                          <Badge key={tag} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default NanoCoreGallery

"use client"

import React, { Suspense, useRef, useState, useEffect, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows } from '@react-three/drei'
import * as THREE from 'three'
import { motion } from 'framer-motion'
import { 
  NanoCoreProps, 
  NanoCoreCustomization, 
  DEFAULT_NANOCORE_CUSTOMIZATION,
  DEFAULT_AVATAR_SETTINGS 
} from '@/types/avatar'
import { Loader2 } from 'lucide-react'

// Core NanoCore Model Component
function NanoCoreModel({ 
  customization, 
  animationState: _animationState,
  onLoad,
  onError: _onError
}: {
  customization: NanoCoreCustomization
  animationState?: any
  onLoad?: () => void
  onError?: (error: Error) => void
}) {
  const groupRef = useRef<THREE.Group>(null)
  const coreRef = useRef<THREE.Mesh>(null)
  const energyCoreRef = useRef<THREE.Mesh>(null)
  const eyesRef = useRef<THREE.Group>(null)
  
  // Animation state
  const [time, setTime] = useState(0)
  
  useFrame((state, delta) => {
    setTime(time + delta)
    
    if (groupRef.current) {
      // Gentle hover animation
      groupRef.current.position.y = Math.sin(time * 2) * 0.05
      groupRef.current.rotation.y += delta * 0.1
    }
    
    if (coreRef.current) {
      // Core glow pulse
      const glowIntensity = customization.coreGlow * (0.8 + Math.sin(time * 3) * 0.2)
      const material = Array.isArray(coreRef.current.material)
        ? coreRef.current.material[0]
        : coreRef.current.material
      if (material && 'emissiveIntensity' in material) {
        (material as any).emissiveIntensity = glowIntensity
      }
    }
    
    if (energyCoreRef.current && customization.energyCore.animation === 'pulse') {
      // Energy core pulse
      const scale = 1 + Math.sin(time * 4) * 0.1
      energyCoreRef.current.scale.setScalar(scale)
    }
    
    if (eyesRef.current && customization.visualSensors.animation === 'blink') {
      // Blinking animation
      const blinkCycle = Math.sin(time * 0.5)
      if (blinkCycle > 0.95) {
        eyesRef.current.scale.y = 0.1
      } else {
        eyesRef.current.scale.y = 1
      }
    }
  })
  
  useEffect(() => {
    onLoad?.()
  }, [onLoad])
  
  // Head Unit Component
  const HeadUnit = useMemo(() => {
    const { headUnit } = customization
    let geometry
    
    switch (headUnit.type) {
      case 'quantum':
        geometry = <octahedronGeometry args={[0.3 * headUnit.size, 0]} />
        break
      case 'neural':
        geometry = <dodecahedronGeometry args={[0.25 * headUnit.size, 0]} />
        break
      case 'advanced':
        geometry = <boxGeometry args={[0.5 * headUnit.size, 0.5 * headUnit.size, 0.5 * headUnit.size]} />
        break
      default: // basic
        geometry = <sphereGeometry args={[0.3 * headUnit.size, 16, 16]} />
    }
    
    return (
      <mesh ref={coreRef} position={[0, 1.2, 0]}>
        {geometry}
        <meshStandardMaterial 
          color={customization.coreColor}
          emissive={customization.coreColor}
          emissiveIntensity={customization.coreGlow * 0.5}
          metalness={headUnit.material === 'metal' ? 0.8 : 0.2}
          roughness={headUnit.material === 'crystal' ? 0.1 : 0.4}
        />
      </mesh>
    )
  }, [customization])
  
  // Visual Sensors (Eyes)
  const VisualSensors = useMemo(() => {
    const { visualSensors } = customization
    
    if (visualSensors.type === 'mono') {
      return (
        <mesh position={[0, 1.2, 0.25]}>
          <sphereGeometry args={[0.05, 8, 8]} />
          <meshStandardMaterial 
            color={visualSensors.color}
            emissive={visualSensors.glow ? visualSensors.color : '#000000'}
            emissiveIntensity={visualSensors.glow ? 0.8 : 0}
          />
        </mesh>
      )
    }
    
    return (
      <group ref={eyesRef} position={[0, 1.2, 0.25]}>
        <mesh position={[-0.08, 0, 0]}>
          <sphereGeometry args={[0.04, 8, 8]} />
          <meshStandardMaterial 
            color={visualSensors.color}
            emissive={visualSensors.glow ? visualSensors.color : '#000000'}
            emissiveIntensity={visualSensors.glow ? 0.8 : 0}
          />
        </mesh>
        <mesh position={[0.08, 0, 0]}>
          <sphereGeometry args={[0.04, 8, 8]} />
          <meshStandardMaterial 
            color={visualSensors.color}
            emissive={visualSensors.glow ? visualSensors.color : '#000000'}
            emissiveIntensity={visualSensors.glow ? 0.8 : 0}
          />
        </mesh>
      </group>
    )
  }, [customization])
  
  // Torso Frame
  const TorsoFrame = useMemo(() => {
    const { torsoFrame } = customization
    
    return (
      <mesh position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.25, 0.3, 0.8, 8]} />
        <meshStandardMaterial 
          color={torsoFrame.color}
          metalness={0.7}
          roughness={0.3}
          transparent={torsoFrame.transparency > 0}
          opacity={1 - torsoFrame.transparency}
        />
      </mesh>
    )
  }, [customization])
  
  // Energy Core
  const EnergyCore = useMemo(() => {
    const { energyCore } = customization
    
    return (
      <mesh ref={energyCoreRef} position={[0, 0.6, 0.2]}>
        <sphereGeometry args={[0.08, 16, 16]} />
        <meshStandardMaterial 
          color={energyCore.color}
          emissive={energyCore.color}
          emissiveIntensity={energyCore.intensity}
          transparent
          opacity={0.8}
        />
      </mesh>
    )
  }, [customization])
  
  // Limbs with enhanced animations
  const Limbs = useMemo(() => {
    const { limbs } = customization

    return (
      <group>
        {/* Arms */}
        <mesh position={[-0.4, 0.4, 0]} rotation={[0, 0, Math.sin(time * 2) * 0.1]}>
          <cylinderGeometry args={[0.06, 0.08, 0.6, 8]} />
          <meshStandardMaterial
            color="#2d3748"
            metalness={limbs.material === 'metal' ? 0.8 : 0.2}
            roughness={0.4}
          />
        </mesh>
        <mesh position={[0.4, 0.4, 0]} rotation={[0, 0, -Math.sin(time * 2) * 0.1]}>
          <cylinderGeometry args={[0.06, 0.08, 0.6, 8]} />
          <meshStandardMaterial
            color="#2d3748"
            metalness={limbs.material === 'metal' ? 0.8 : 0.2}
            roughness={0.4}
          />
        </mesh>

        {/* Legs */}
        <mesh position={[-0.15, -0.2, 0]}>
          <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
          <meshStandardMaterial
            color="#2d3748"
            metalness={limbs.material === 'metal' ? 0.8 : 0.2}
            roughness={0.4}
          />
        </mesh>
        <mesh position={[0.15, -0.2, 0]}>
          <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
          <meshStandardMaterial
            color="#2d3748"
            metalness={limbs.material === 'metal' ? 0.8 : 0.2}
            roughness={0.4}
          />
        </mesh>
      </group>
    )
  }, [customization, time])

  // Particle Effects for advanced stages
  const ParticleEffects = useMemo(() => {
    if (!customization.evolutionStage.visualUpgrades.particleEffects) return null

    return (
      <group>
        {/* Floating energy particles around the avatar */}
        {Array.from({ length: 8 }).map((_, i) => (
          <mesh
            key={i}
            position={[
              Math.cos((time + i) * 0.5) * 1.5,
              Math.sin((time + i) * 0.3) * 0.5 + 1,
              Math.sin((time + i) * 0.5) * 1.5
            ]}
          >
            <sphereGeometry args={[0.02, 8, 8]} />
            <meshStandardMaterial
              color={customization.energyCore.color}
              emissive={customization.energyCore.color}
              emissiveIntensity={0.8}
              transparent
              opacity={0.6}
            />
          </mesh>
        ))}
      </group>
    )
  }, [customization, time])

  // Circuit patterns for advanced stages
  const CircuitPatterns = useMemo(() => {
    if (customization.evolutionStage.visualUpgrades.circuitComplexity < 2) return null

    return (
      <group>
        {/* Circuit lines on torso */}
        <mesh position={[0, 0.5, 0.31]}>
          <planeGeometry args={[0.4, 0.6]} />
          <meshStandardMaterial
            color={customization.energyCore.color}
            emissive={customization.energyCore.color}
            emissiveIntensity={0.3}
            transparent
            opacity={0.4}
          />
        </mesh>
      </group>
    )
  }, [customization])

  // Accessories based on evolution stage
  const Accessories = useMemo(() => {
    const { accessories: _accessories, evolutionStage } = customization

    return (
      <group>
        {/* Halo for mentor+ stages */}
        {evolutionStage.stage === 'mentor' || evolutionStage.stage === 'hero' ? (
          <mesh position={[0, 1.8, 0]} rotation={[0, time, 0]}>
            <torusGeometry args={[0.4, 0.02, 8, 32]} />
            <meshStandardMaterial
              color={customization.energyCore.color}
              emissive={customization.energyCore.color}
              emissiveIntensity={0.8}
              transparent
              opacity={0.7}
            />
          </mesh>
        ) : null}

        {/* Energy wings for hero stage */}
        {evolutionStage.stage === 'hero' ? (
          <group position={[0, 0.5, -0.3]}>
            <mesh position={[-0.3, 0, 0]} rotation={[0, 0.3, Math.sin(time * 3) * 0.1]}>
              <planeGeometry args={[0.2, 0.8]} />
              <meshStandardMaterial
                color={customization.energyCore.color}
                emissive={customization.energyCore.color}
                emissiveIntensity={0.6}
                transparent
                opacity={0.5}
              />
            </mesh>
            <mesh position={[0.3, 0, 0]} rotation={[0, -0.3, -Math.sin(time * 3) * 0.1]}>
              <planeGeometry args={[0.2, 0.8]} />
              <meshStandardMaterial
                color={customization.energyCore.color}
                emissive={customization.energyCore.color}
                emissiveIntensity={0.6}
                transparent
                opacity={0.5}
              />
            </mesh>
          </group>
        ) : null}
      </group>
    )
  }, [customization, time])
  
  return (
    <group ref={groupRef}>
      {HeadUnit}
      {VisualSensors}
      {TorsoFrame}
      {EnergyCore}
      {Limbs}
      {ParticleEffects}
      {CircuitPatterns}
      {Accessories}
    </group>
  )
}

// Loading component
function NanoCoreLoader() {
  return (
    <div className="flex items-center justify-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-cyan-400" />
      <span className="text-cyan-400 font-medium">Initializing NanoCore...</span>
    </div>
  )
}

// Main NanoCore Component
export function NanoCore({
  customization = DEFAULT_NANOCORE_CUSTOMIZATION,
  settings = DEFAULT_AVATAR_SETTINGS,
  animationState,
  aiPersonality,
  interactionMode = 'showcase',
  onLoad,
  onError,
  onInteraction,
  className = "",
  style
}: NanoCoreProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [_cameraDistance, _setCameraDistance] = useState(settings.cameraDistance)
  
  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }
  
  const _handleInteraction = (type: string, data?: any) => {
    onInteraction?.(type, data)
  }
  
  return (
    <div className={`relative w-full h-full bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden ${className}`} style={style}>
      {/* 3D Canvas */}
      <Canvas
        shadows
        camera={{ position: [0, 0, _cameraDistance], fov: 50 }}
        gl={{ antialias: true, alpha: true }}
      >
        <Suspense fallback={null}>
          {/* Lighting optimized for NanoCore */}
          <ambientLight intensity={settings.ambientLightIntensity} />
          <directionalLight 
            position={[5, 5, 5]} 
            intensity={settings.directionalLightIntensity}
            castShadow
            shadow-mapSize-width={1024}
            shadow-mapSize-height={1024}
          />
          <pointLight position={[-5, 5, 5]} intensity={0.3} color="#00ffff" />
          <pointLight position={[5, -5, -5]} intensity={0.2} color="#ff00ff" />
          
          {/* Environment */}
          <Environment preset="city" />
          
          {/* NanoCore Model */}
          <NanoCoreModel
            customization={customization}
            animationState={animationState}
            onLoad={handleLoad}
            onError={onError}
          />
          
          {/* Ground shadow */}
          <ContactShadows 
            position={[0, -0.8, 0]} 
            opacity={0.4} 
            scale={3} 
            blur={2} 
            far={2} 
          />
          
          {/* Camera controls */}
          <OrbitControls
            enablePan={false}
            enableZoom={true}
            enableRotate={true}
            autoRotate={settings.autoRotate}
            autoRotateSpeed={2}
            minDistance={1}
            maxDistance={10}
            minPolarAngle={Math.PI / 6}
            maxPolarAngle={Math.PI - Math.PI / 6}
          />
        </Suspense>
      </Canvas>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <NanoCoreLoader />
        </div>
      )}
      
      {/* Interaction overlay for assistant mode */}
      {interactionMode === 'assistant' && isLoaded && (
        <div className="absolute bottom-4 left-4 right-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-cyan-400/30"
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
              <span className="text-cyan-400 text-sm font-medium">
                {aiPersonality?.name || 'Nano'} is ready to help!
              </span>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default NanoCore

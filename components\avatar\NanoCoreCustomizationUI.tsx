"use client"

import React, { useState } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Sparkles, 
  RotateCcw,
  Save,
  Download,
  Upload
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>lide<PERSON> } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Nano<PERSON><PERSON><PERSON>anilla } from './NanoCoreVanilla'
import { 
  NanoCoreCustomization, 
  DEFAULT_NANOCORE_CUSTOMIZATION 
} from '@/types/avatar'

interface NanoCoreCustomizationUIProps {
  onSave?: (customization: NanoCoreCustomization) => void
  className?: string
}

export function NanoCoreCustomizationUI({
  onSave,
  className = ""
}: NanoCoreCustomizationUIProps) {
  const [customization, setCustomization] = useState<NanoCoreCustomization>(DEFAULT_NANOCORE_CUSTOMIZATION)
  const [activeTab, setActiveTab] = useState<'core' | 'appearance' | 'effects'>('core')

  // Color palettes
  const coreColors = [
    '#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600', '#6600ff', '#ff0066', '#ffffff'
  ]

  const frameColors = [
    '#2d3748', '#4a5568', '#718096', '#a0aec0', '#1a202c', '#2d3748', '#4a5568'
  ]

  const updateCustomization = (updates: Partial<NanoCoreCustomization>) => {
    setCustomization(prev => ({ ...prev, ...updates }))
  }

  const resetToDefault = () => {
    setCustomization(DEFAULT_NANOCORE_CUSTOMIZATION)
  }

  const handleSave = () => {
    onSave?.(customization)
  }

  const handleExport = () => {
    const data = JSON.stringify(customization, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `nanocore-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        setCustomization(data)
      } catch (error) {
        console.error('Failed to import customization:', error)
      }
    }
    reader.readAsText(file)
  }

  const ColorPicker = ({ 
    colors, 
    currentColor, 
    onChange, 
    label 
  }: { 
    colors: string[]
    currentColor: string
    onChange: (color: string) => void
    label: string 
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-gray-300">{label}</Label>
      <div className="flex gap-2 flex-wrap">
        {colors.map((color) => (
          <button
            key={color}
            className={`w-8 h-8 rounded-full border-2 transition-all ${
              currentColor === color 
                ? 'border-cyan-400 scale-110' 
                : 'border-gray-600 hover:border-gray-400'
            }`}
            style={{ backgroundColor: color }}
            onClick={() => onChange(color)}
          />
        ))}
      </div>
    </div>
  )

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${className}`}>
      {/* 3D Preview */}
      <div className="space-y-4">
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Live Preview
              </span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  className="border-cyan-600 text-cyan-400"
                >
                  <Save className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetToDefault}
                  className="border-gray-600"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-96 lg:h-[500px]">
              <NanoCoreVanilla
                customization={customization}
                className="rounded-b-lg"
              />
            </div>
          </CardContent>
        </Card>

        {/* Export/Import */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardContent className="p-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="flex-1 border-gray-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('import-input')?.click()}
                className="flex-1 border-gray-600"
              >
                <Upload className="w-4 h-4 mr-2" />
                Import
              </Button>
              <input
                id="import-input"
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customization Controls */}
      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="flex border border-gray-700 rounded-lg overflow-hidden">
          <Button
            variant={activeTab === 'core' ? 'default' : 'ghost'}
            onClick={() => setActiveTab('core')}
            className="flex-1 rounded-none"
          >
            <Zap className="w-4 h-4 mr-2" />
            Core
          </Button>
          <Button
            variant={activeTab === 'appearance' ? 'default' : 'ghost'}
            onClick={() => setActiveTab('appearance')}
            className="flex-1 rounded-none"
          >
            <Palette className="w-4 h-4 mr-2" />
            Appearance
          </Button>
          <Button
            variant={activeTab === 'effects' ? 'default' : 'ghost'}
            onClick={() => setActiveTab('effects')}
            className="flex-1 rounded-none"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Effects
          </Button>
        </div>

        {/* Core Tab */}
        {activeTab === 'core' && (
          <Card className="bg-gray-900/95 border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-cyan-400">Core Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Core Type */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-300">Core Type</Label>
                <div className="grid grid-cols-2 gap-2">
                  {['sphere', 'crystal', 'geometric', 'organic'].map((type) => (
                    <Button
                      key={type}
                      variant={customization.coreType === type ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => updateCustomization({ coreType: type as any })}
                      className="text-xs capitalize"
                    >
                      {type}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Core Color */}
              <ColorPicker
                colors={coreColors}
                currentColor={customization.coreColor}
                onChange={(color) => updateCustomization({ coreColor: color })}
                label="Core Color"
              />

              {/* Core Glow */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-300">
                  Core Glow: {Math.round(customization.coreGlow * 100)}%
                </Label>
                <Slider
                  value={[customization.coreGlow]}
                  onValueChange={([value]) => updateCustomization({ coreGlow: value })}
                  max={1}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* Energy Core */}
              <div className="space-y-3 border-t border-gray-700 pt-4">
                <Label className="text-sm font-medium text-cyan-400">Energy Core</Label>
                
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">Type</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {['spark', 'atom', 'vortex', 'quantum'].map((type) => (
                      <Button
                        key={type}
                        variant={customization.energyCore.type === type ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateCustomization({ 
                          energyCore: { ...customization.energyCore, type: type as any }
                        })}
                        className="text-xs capitalize"
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                <ColorPicker
                  colors={coreColors}
                  currentColor={customization.energyCore.color}
                  onChange={(color) => updateCustomization({ 
                    energyCore: { ...customization.energyCore, color }
                  })}
                  label="Energy Color"
                />

                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">
                    Intensity: {Math.round(customization.energyCore.intensity * 100)}%
                  </Label>
                  <Slider
                    value={[customization.energyCore.intensity]}
                    onValueChange={([value]) => updateCustomization({ 
                      energyCore: { ...customization.energyCore, intensity: value }
                    })}
                    max={1}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Appearance Tab */}
        {activeTab === 'appearance' && (
          <Card className="bg-gray-900/95 border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-cyan-400">Appearance Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Head Unit */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-cyan-400">Head Unit</Label>
                
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">Type</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {['basic', 'advanced', 'quantum', 'neural'].map((type) => (
                      <Button
                        key={type}
                        variant={customization.headUnit.type === type ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateCustomization({ 
                          headUnit: { ...customization.headUnit, type: type as any }
                        })}
                        className="text-xs capitalize"
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">
                    Size: {Math.round(customization.headUnit.size * 100)}%
                  </Label>
                  <Slider
                    value={[customization.headUnit.size]}
                    onValueChange={([value]) => updateCustomization({ 
                      headUnit: { ...customization.headUnit, size: value }
                    })}
                    max={1.5}
                    min={0.8}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Visual Sensors */}
              <div className="space-y-3 border-t border-gray-700 pt-4">
                <Label className="text-sm font-medium text-cyan-400">Visual Sensors</Label>
                
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">Type</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {['mono', 'dual', 'scanner', 'compound'].map((type) => (
                      <Button
                        key={type}
                        variant={customization.visualSensors.type === type ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => updateCustomization({ 
                          visualSensors: { ...customization.visualSensors, type: type as any }
                        })}
                        className="text-xs capitalize"
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                <ColorPicker
                  colors={coreColors}
                  currentColor={customization.visualSensors.color}
                  onChange={(color) => updateCustomization({ 
                    visualSensors: { ...customization.visualSensors, color }
                  })}
                  label="Sensor Color"
                />

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={customization.visualSensors.glow}
                    onCheckedChange={(checked) => updateCustomization({ 
                      visualSensors: { ...customization.visualSensors, glow: checked }
                    })}
                  />
                  <Label className="text-sm text-gray-300">Glow Effect</Label>
                </div>
              </div>

              {/* Torso Frame */}
              <div className="space-y-3 border-t border-gray-700 pt-4">
                <Label className="text-sm font-medium text-cyan-400">Torso Frame</Label>
                
                <ColorPicker
                  colors={frameColors}
                  currentColor={customization.torsoFrame.color}
                  onChange={(color) => updateCustomization({ 
                    torsoFrame: { ...customization.torsoFrame, color }
                  })}
                  label="Frame Color"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Effects Tab */}
        {activeTab === 'effects' && (
          <Card className="bg-gray-900/95 border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-cyan-400">Visual Effects</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-300">Particle Effects</Label>
                    <p className="text-xs text-gray-400">Floating energy particles around avatar</p>
                  </div>
                  <Switch
                    checked={customization.energyCore.particles}
                    onCheckedChange={(checked) => updateCustomization({ 
                      energyCore: { ...customization.energyCore, particles: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-300">Advanced Animations</Label>
                    <p className="text-xs text-gray-400">Enhanced movement and transitions</p>
                  </div>
                  <Switch
                    checked={customization.evolutionStage.visualUpgrades.advancedAnimations}
                    onCheckedChange={(checked) => updateCustomization({ 
                      evolutionStage: { 
                        ...customization.evolutionStage,
                        visualUpgrades: {
                          ...customization.evolutionStage.visualUpgrades,
                          advancedAnimations: checked
                        }
                      }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-300">Custom Shapes</Label>
                    <p className="text-xs text-gray-400">Unlock geometric variations</p>
                  </div>
                  <Switch
                    checked={customization.evolutionStage.visualUpgrades.customShapes}
                    onCheckedChange={(checked) => updateCustomization({ 
                      evolutionStage: { 
                        ...customization.evolutionStage,
                        visualUpgrades: {
                          ...customization.evolutionStage.visualUpgrades,
                          customShapes: checked
                        }
                      }
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-300">
                    Circuit Complexity: {customization.evolutionStage.visualUpgrades.circuitComplexity}
                  </Label>
                  <Slider
                    value={[customization.evolutionStage.visualUpgrades.circuitComplexity]}
                    onValueChange={([value]) => updateCustomization({ 
                      evolutionStage: { 
                        ...customization.evolutionStage,
                        visualUpgrades: {
                          ...customization.evolutionStage.visualUpgrades,
                          circuitComplexity: value
                        }
                      }
                    })}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-300">
                    Glow Intensity: {Math.round(customization.evolutionStage.visualUpgrades.glowIntensity * 100)}%
                  </Label>
                  <Slider
                    value={[customization.evolutionStage.visualUpgrades.glowIntensity]}
                    onValueChange={([value]) => updateCustomization({ 
                      evolutionStage: { 
                        ...customization.evolutionStage,
                        visualUpgrades: {
                          ...customization.evolutionStage.visualUpgrades,
                          glowIntensity: value
                        }
                      }
                    })}
                    max={1}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default NanoCoreCustomizationUI

"use client"

import React, { useState, useEffect } from 'react'
import { AnimatePresence } from 'framer-motion'
import { EpochTownsOnboarding } from './EpochTownsOnboarding'
import { WelcomeWizard } from './WelcomeWizard'
import { InteractiveTutorial } from './InteractiveTutorial'

interface OnboardingState {
  hasSeenIntro: boolean
  hasCompletedWizard: boolean
  hasCompletedFirstVisit: boolean
  hasCompletedFirstContribution: boolean
  hasCompletedFirstBuilding: boolean
  hasCompletedFirstSocial: boolean
  currentTutorial: string | null
  userProfile: any | null
}

interface OnboardingManagerProps {
  userId: string
  isNewUser?: boolean
  forceShow?: boolean
  onComplete?: () => void
  className?: string
}

export function OnboardingManager({
  userId,
  isNewUser = false,
  forceShow = false,
  onComplete,
  className = ""
}: OnboardingManagerProps) {
  const [onboardingState, setOnboardingState] = useState<OnboardingState>({
    hasSeenIntro: false,
    hasCompletedWizard: false,
    hasCompletedFirstVisit: false,
    hasCompletedFirstContribution: false,
    hasCompletedFirstBuilding: false,
    hasCompletedFirstSocial: false,
    currentTutorial: null,
    userProfile: null
  })

  const [currentFlow, setCurrentFlow] = useState<string | null>(null)

  // Load onboarding state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem(`onboarding_${userId}`)
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState)
        setOnboardingState(parsed)
      } catch (error) {
        console.error('Failed to parse onboarding state:', error)
      }
    }
  }, [userId])

  // Save onboarding state to localStorage
  const saveOnboardingState = (newState: Partial<OnboardingState>) => {
    const updatedState = { ...onboardingState, ...newState }
    setOnboardingState(updatedState)
    localStorage.setItem(`onboarding_${userId}`, JSON.stringify(updatedState))
  }

  // Determine what onboarding flow to show
  useEffect(() => {
    if (forceShow) {
      setCurrentFlow('intro')
      return
    }

    if (isNewUser && !onboardingState.hasSeenIntro) {
      setCurrentFlow('intro')
    } else if (!onboardingState.hasCompletedWizard && !onboardingState.userProfile) {
      setCurrentFlow('wizard')
    } else if (!onboardingState.hasCompletedFirstVisit) {
      setCurrentFlow('first-visit')
    } else if (onboardingState.currentTutorial) {
      setCurrentFlow(onboardingState.currentTutorial)
    } else {
      setCurrentFlow(null)
    }
  }, [isNewUser, onboardingState, forceShow])

  // Handle intro completion
  const handleIntroComplete = () => {
    saveOnboardingState({ hasSeenIntro: true })
    setCurrentFlow('wizard')
  }

  // Handle wizard completion
  const handleWizardComplete = (userProfile: any) => {
    saveOnboardingState({ 
      hasCompletedWizard: true, 
      userProfile 
    })
    setCurrentFlow('first-visit')
  }

  // Handle tutorial completion
  const handleTutorialComplete = (tutorialType: string) => {
    const updates: Partial<OnboardingState> = { currentTutorial: null }
    
    switch (tutorialType) {
      case 'first-visit':
        updates.hasCompletedFirstVisit = true
        break
      case 'first-contribution':
        updates.hasCompletedFirstContribution = true
        break
      case 'first-building':
        updates.hasCompletedFirstBuilding = true
        break
      case 'first-social':
        updates.hasCompletedFirstSocial = true
        break
    }
    
    saveOnboardingState(updates)
    setCurrentFlow(null)
    
    if (onComplete) {
      onComplete()
    }
  }

  // Handle tutorial skip
  const handleTutorialSkip = (tutorialType: string) => {
    handleTutorialComplete(tutorialType)
  }

  // Public methods for triggering specific tutorials
  const _triggerTutorial = (tutorialType: string) => {
    saveOnboardingState({ currentTutorial: tutorialType })
    setCurrentFlow(tutorialType)
  }

  // Check if user should see a specific tutorial
  const _shouldShowTutorial = (tutorialType: string) => {
    switch (tutorialType) {
      case 'first-visit':
        return !onboardingState.hasCompletedFirstVisit
      case 'first-contribution':
        return !onboardingState.hasCompletedFirstContribution
      case 'first-building':
        return !onboardingState.hasCompletedFirstBuilding
      case 'first-social':
        return !onboardingState.hasCompletedFirstSocial
      default:
        return false
    }
  }

  // Reset onboarding (for testing or user request)
  const _resetOnboarding = () => {
    localStorage.removeItem(`onboarding_${userId}`)
    setOnboardingState({
      hasSeenIntro: false,
      hasCompletedWizard: false,
      hasCompletedFirstVisit: false,
      hasCompletedFirstContribution: false,
      hasCompletedFirstBuilding: false,
      hasCompletedFirstSocial: false,
      currentTutorial: null,
      userProfile: null
    })
    setCurrentFlow('intro')
  }

  // Get onboarding progress
  const _getProgress = () => {
    const totalSteps = 6 // intro, wizard, first-visit, first-contribution, first-building, first-social
    let completedSteps = 0

    if (onboardingState.hasSeenIntro) completedSteps++
    if (onboardingState.hasCompletedWizard) completedSteps++
    if (onboardingState.hasCompletedFirstVisit) completedSteps++
    if (onboardingState.hasCompletedFirstContribution) completedSteps++
    if (onboardingState.hasCompletedFirstBuilding) completedSteps++
    if (onboardingState.hasCompletedFirstSocial) completedSteps++
    
    return {
      completed: completedSteps,
      total: totalSteps,
      percentage: (completedSteps / totalSteps) * 100
    }
  }

  // Methods are exposed through the return object

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {/* Intro Onboarding */}
        {currentFlow === 'intro' && (
          <EpochTownsOnboarding
            key="intro"
            isOpen={true}
            onComplete={handleIntroComplete}
            onSkip={handleIntroComplete}
            userProfile={{
              name: onboardingState.userProfile?.displayName || 'Explorer',
              joinDate: new Date(),
              interests: onboardingState.userProfile?.interests || []
            }}
          />
        )}

        {/* Welcome Wizard */}
        {currentFlow === 'wizard' && (
          <WelcomeWizard
            key="wizard"
            isOpen={true}
            onComplete={handleWizardComplete}
            onSkip={() => handleWizardComplete({})}
            initialData={onboardingState.userProfile}
          />
        )}

        {/* Interactive Tutorials */}
        {currentFlow === 'first-visit' && (
          <InteractiveTutorial
            key="first-visit"
            isActive={true}
            onComplete={() => handleTutorialComplete('first-visit')}
            onSkip={() => handleTutorialSkip('first-visit')}
            tutorialType="first-visit"
          />
        )}

        {currentFlow === 'first-contribution' && (
          <InteractiveTutorial
            key="first-contribution"
            isActive={true}
            onComplete={() => handleTutorialComplete('first-contribution')}
            onSkip={() => handleTutorialSkip('first-contribution')}
            tutorialType="first-contribution"
          />
        )}

        {currentFlow === 'first-building' && (
          <InteractiveTutorial
            key="first-building"
            isActive={true}
            onComplete={() => handleTutorialComplete('first-building')}
            onSkip={() => handleTutorialSkip('first-building')}
            tutorialType="first-building"
          />
        )}

        {currentFlow === 'first-social' && (
          <InteractiveTutorial
            key="first-social"
            isActive={true}
            onComplete={() => handleTutorialComplete('first-social')}
            onSkip={() => handleTutorialSkip('first-social')}
            tutorialType="first-social"
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Hook for using onboarding in components
export function useOnboarding(_userId: string) {
  const [manager, setManager] = useState<any>(null)

  const triggerTutorial = (tutorialType: string) => {
    if (manager) {
      manager.triggerTutorial(tutorialType)
    }
  }

  const shouldShowTutorial = (tutorialType: string) => {
    if (manager) {
      return manager.shouldShowTutorial(tutorialType)
    }
    return false
  }

  const resetOnboarding = () => {
    if (manager) {
      manager.resetOnboarding()
    }
  }

  const getProgress = () => {
    if (manager) {
      return manager.getProgress()
    }
    return { completed: 0, total: 6, percentage: 0 }
  }

  return {
    triggerTutorial,
    shouldShowTutorial,
    resetOnboarding,
    getProgress,
    setManager
  }
}

export default OnboardingManager

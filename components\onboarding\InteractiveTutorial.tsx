"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Target, 
  ArrowRight,
  X,
  Lightbulb,
  Users,
  Building,
  Heart,
  Brain,
  Hammer,
  Search,
  Sparkles,
  Trophy,
  Zap
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'

interface TutorialStep {
  id: string
  title: string
  description: string
  target: string // CSS selector for highlighting
  action: 'click' | 'hover' | 'input' | 'wait'
  position: 'top' | 'bottom' | 'left' | 'right'
  content: React.ReactNode
  validation?: () => boolean
  reward?: {
    xp: number
    badge?: string
    message: string
  }
}

interface InteractiveTutorialProps {
  isActive: boolean
  onComplete: () => void
  onSkip: () => void
  tutorialType: 'first-visit' | 'first-contribution' | 'first-building' | 'first-social'
  className?: string
}

export function InteractiveTutorial({
  isActive,
  onComplete,
  onSkip,
  tutorialType,
  className = ""
}: InteractiveTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<string[]>([])
  const [_highlightedElement, setHighlightedElement] = useState<string | null>(null)
  const [totalXP, setTotalXP] = useState(0)

  const tutorialSteps: Record<string, TutorialStep[]> = {
    'first-visit': [
      {
        id: 'welcome',
        title: 'Welcome to Your Town!',
        description: 'Let\'s explore your collaborative space',
        target: '.town-overview',
        action: 'wait',
        position: 'bottom',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              This is your town dashboard where you can see your community&apos;s progress and DNA.
            </p>
            <div className="flex items-center gap-2 text-sm text-cyan-400">
              <Lightbulb className="w-4 h-4" />
              <span>Your actions here will shape your town&apos;s future!</span>
            </div>
          </div>
        ),
        reward: { xp: 10, message: 'Welcome bonus!' }
      },
      {
        id: 'stats',
        title: 'Town Stats',
        description: 'Check out your town\'s current traits',
        target: '.town-stats',
        action: 'hover',
        position: 'left',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              These bars show your town&apos;s genetic traits. They grow based on community actions.
            </p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <Heart className="w-3 h-3 text-red-400" />
                <span className="text-gray-400">Empathy</span>
              </div>
              <div className="flex items-center gap-1">
                <Brain className="w-3 h-3 text-blue-400" />
                <span className="text-gray-400">Wisdom</span>
              </div>
              <div className="flex items-center gap-1">
                <Hammer className="w-3 h-3 text-yellow-400" />
                <span className="text-gray-400">Builder</span>
              </div>
              <div className="flex items-center gap-1">
                <Search className="w-3 h-3 text-green-400" />
                <span className="text-gray-400">Curiosity</span>
              </div>
            </div>
          </div>
        ),
        reward: { xp: 15, message: 'Stats explorer!' }
      },
      {
        id: 'dna',
        title: 'DNA Visualization',
        description: 'See your town\'s genetic code',
        target: '.dna-helix',
        action: 'click',
        position: 'top',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              This DNA helix represents your town&apos;s unique genetic makeup.
            </p>
            <p className="text-sm text-gray-400">
              Click on it to see detailed trait information!
            </p>
          </div>
        ),
        reward: { xp: 20, badge: 'DNA Explorer', message: 'Genetic code unlocked!' }
      },
      {
        id: 'world-tab',
        title: 'Explore the 3D World',
        description: 'Visit your town\'s 3D environment',
        target: '[data-tab="world"]',
        action: 'click',
        position: 'bottom',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Click the &quot;3D World&quot; tab to explore your town in three dimensions!
            </p>
            <div className="flex items-center gap-2 text-sm text-green-400">
              <Building className="w-4 h-4" />
              <span>You can build and interact with other citizens here</span>
            </div>
          </div>
        ),
        reward: { xp: 25, message: 'World explorer!' }
      }
    ],
    'first-contribution': [
      {
        id: 'contribute-tab',
        title: 'Make Your First Contribution',
        description: 'Help your town grow stronger',
        target: '[data-tab="contribute"]',
        action: 'click',
        position: 'bottom',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Ready to make your first contribution? Click the &quot;Contribute&quot; tab!
            </p>
            <p className="text-sm text-gray-400">
              Every contribution strengthens your town&apos;s traits and helps unlock new areas.
            </p>
          </div>
        ),
        reward: { xp: 15, message: 'Ready to contribute!' }
      },
      {
        id: 'choose-action',
        title: 'Choose Your Action',
        description: 'Pick how you want to help',
        target: '.contribution-actions',
        action: 'hover',
        position: 'right',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Choose an action that matches your interests:
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4 text-blue-400" />
                <span className="text-gray-300">Share knowledge</span>
              </div>
              <div className="flex items-center gap-2">
                <Heart className="w-4 h-4 text-red-400" />
                <span className="text-gray-300">Help others</span>
              </div>
              <div className="flex items-center gap-2">
                <Hammer className="w-4 h-4 text-yellow-400" />
                <span className="text-gray-300">Build something</span>
              </div>
              <div className="flex items-center gap-2">
                <Search className="w-4 h-4 text-green-400" />
                <span className="text-gray-300">Ask questions</span>
              </div>
            </div>
          </div>
        ),
        reward: { xp: 20, message: 'Action selected!' }
      },
      {
        id: 'submit-contribution',
        title: 'Submit Your Contribution',
        description: 'Complete your first action',
        target: '.submit-contribution',
        action: 'click',
        position: 'top',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Great! Now click submit to make your first contribution to the town.
            </p>
            <div className="flex items-center gap-2 text-sm text-cyan-400">
              <Sparkles className="w-4 h-4" />
              <span>This will earn you XP and strengthen your town&apos;s DNA!</span>
            </div>
          </div>
        ),
        reward: { xp: 50, badge: 'First Contributor', message: 'First contribution complete!' }
      }
    ],
    'first-building': [
      {
        id: 'building-intro',
        title: 'Building in Your Town',
        description: 'Learn how to construct in the 3D world',
        target: '.building-controls',
        action: 'wait',
        position: 'bottom',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Welcome to the building interface! Here you can add structures to your town.
            </p>
            <p className="text-sm text-gray-400">
              Buildings unlock new features and strengthen your town&apos;s traits.
            </p>
          </div>
        ),
        reward: { xp: 15, message: 'Builder mode activated!' }
      },
      {
        id: 'select-building',
        title: 'Choose a Building',
        description: 'Pick your first structure',
        target: '.building-selector',
        action: 'click',
        position: 'right',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Select a building type from the available options.
            </p>
            <div className="text-sm text-gray-400">
              <p>🏛️ Library - Boosts wisdom</p>
              <p>❤️ Garden - Increases empathy</p>
              <p>🔨 Workshop - Enhances building</p>
              <p>🔬 Lab - Sparks curiosity</p>
            </div>
          </div>
        ),
        reward: { xp: 20, message: 'Building selected!' }
      },
      {
        id: 'place-building',
        title: 'Place Your Building',
        description: 'Click on the 3D world to place it',
        target: '.world-canvas',
        action: 'click',
        position: 'top',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Click anywhere in the 3D world to place your building!
            </p>
            <div className="flex items-center gap-2 text-sm text-green-400">
              <Building className="w-4 h-4" />
              <span>Your building will appear and benefit the whole town</span>
            </div>
          </div>
        ),
        reward: { xp: 75, badge: 'Master Builder', message: 'First building constructed!' }
      }
    ],
    'first-social': [
      {
        id: 'social-intro',
        title: 'Connect with Citizens',
        description: 'Meet your fellow town members',
        target: '.citizen-list',
        action: 'wait',
        position: 'left',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              These are your fellow citizens! You can interact with them and collaborate.
            </p>
            <p className="text-sm text-gray-400">
              Building relationships strengthens your town&apos;s empathy trait.
            </p>
          </div>
        ),
        reward: { xp: 10, message: 'Social mode activated!' }
      },
      {
        id: 'greet-citizen',
        title: 'Greet a Citizen',
        description: 'Click on someone to say hello',
        target: '.citizen-avatar',
        action: 'click',
        position: 'bottom',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Click on a citizen&apos;s avatar to interact with them!
            </p>
            <div className="flex items-center gap-2 text-sm text-cyan-400">
              <Users className="w-4 h-4" />
              <span>Start building your network</span>
            </div>
          </div>
        ),
        reward: { xp: 30, message: 'First connection made!' }
      },
      {
        id: 'send-message',
        title: 'Send a Message',
        description: 'Communicate with your new friend',
        target: '.chat-input',
        action: 'input',
        position: 'top',
        content: (
          <div className="space-y-3">
            <p className="text-gray-300">
              Type a friendly message to introduce yourself!
            </p>
            <p className="text-sm text-gray-400">
              Good communication builds empathy and strengthens community bonds.
            </p>
          </div>
        ),
        reward: { xp: 40, badge: 'Social Butterfly', message: 'First message sent!' }
      }
    ]
  }

  const steps = tutorialSteps[tutorialType] || []
  const currentStepData = steps[currentStep]
  const progress = steps.length > 0 ? ((currentStep + 1) / steps.length) * 100 : 0

  const nextStep = () => {
    if (currentStepData?.reward) {
      setTotalXP(prev => prev + currentStepData.reward!.xp)
    }
    
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => [...prev, currentStepData.id])
      setCurrentStep(currentStep + 1)
    } else {
      setCompletedSteps(prev => [...prev, currentStepData.id])
      onComplete()
    }
  }

  const skipTutorial = () => {
    onSkip()
  }

  // Highlight target element
  useEffect(() => {
    if (currentStepData?.target) {
      setHighlightedElement(currentStepData.target)
      
      // Add highlight class to target element
      const element = document.querySelector(currentStepData.target)
      if (element) {
        element.classList.add('tutorial-highlight')
      }
      
      return () => {
        // Remove highlight class
        const element = document.querySelector(currentStepData.target)
        if (element) {
          element.classList.remove('tutorial-highlight')
        }
      }
    }
  }, [currentStepData])

  if (!isActive || !currentStepData) return null

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 pointer-events-none" />
      
      {/* Tutorial Card */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={`fixed z-50 max-w-sm ${className}`}
        style={{
          // Position based on target element and position preference
          top: currentStepData.position === 'bottom' ? 'auto' : '20%',
          bottom: currentStepData.position === 'top' ? 'auto' : '20%',
          left: currentStepData.position === 'right' ? '20px' : 'auto',
          right: currentStepData.position === 'left' ? '20px' : 'auto',
          transform: currentStepData.position === 'top' || currentStepData.position === 'bottom' 
            ? 'translateX(-50%)' : 'none',
          marginLeft: currentStepData.position === 'top' || currentStepData.position === 'bottom' 
            ? '50%' : '0'
        }}
      >
        <Card className="bg-gray-900 border-cyan-400 shadow-xl">
          <CardContent className="p-4">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg">
                  <Target className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">{currentStepData.title}</h3>
                  <p className="text-xs text-gray-400">Step {currentStep + 1} of {steps.length}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={skipTutorial}
                className="w-6 h-6 p-0 text-gray-400 hover:text-white"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>

            {/* Progress */}
            <Progress value={progress} className="h-1 mb-3" />

            {/* Content */}
            <div className="space-y-3">
              <p className="text-sm text-gray-300">{currentStepData.description}</p>
              {currentStepData.content}
            </div>

            {/* Reward Preview */}
            {currentStepData.reward && (
              <div className="mt-3 p-2 bg-gradient-to-r from-green-900/50 to-cyan-900/50 rounded border border-green-600/30">
                <div className="flex items-center gap-2 text-xs">
                  <Trophy className="w-3 h-3 text-yellow-400" />
                  <span className="text-green-400">+{currentStepData.reward.xp} XP</span>
                  {currentStepData.reward.badge && (
                    <Badge variant="outline" className="text-xs border-purple-400 text-purple-400">
                      {currentStepData.reward.badge}
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <Zap className="w-3 h-3" />
                <span>Total XP: {totalXP}</span>
              </div>
              
              <div className="flex gap-2">
                {currentStepData.action === 'wait' && (
                  <Button
                    size="sm"
                    onClick={nextStep}
                    className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600"
                  >
                    Continue
                    <ArrowRight className="w-3 h-3 ml-1" />
                  </Button>
                )}
                
                {currentStepData.action !== 'wait' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={nextStep}
                  >
                    {currentStepData.action === 'click' ? 'Click to continue' :
                     currentStepData.action === 'hover' ? 'Hover to continue' :
                     currentStepData.action === 'input' ? 'Type to continue' : 'Continue'}
                  </Button>
                )}
              </div>
            </div>

            {/* Completion indicator */}
            <div className="flex items-center gap-1 mt-3">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`w-1.5 h-1.5 rounded-full transition-colors ${
                    completedSteps.includes(step.id)
                      ? 'bg-green-400'
                      : index === currentStep
                        ? 'bg-cyan-400'
                        : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pointer arrow */}
        <div 
          className={`absolute w-0 h-0 border-8 ${
            currentStepData.position === 'top' ? 'border-t-transparent border-l-transparent border-r-transparent border-b-cyan-400 -top-2 left-1/2 transform -translate-x-1/2' :
            currentStepData.position === 'bottom' ? 'border-b-transparent border-l-transparent border-r-transparent border-t-cyan-400 -bottom-2 left-1/2 transform -translate-x-1/2' :
            currentStepData.position === 'left' ? 'border-l-transparent border-t-transparent border-b-transparent border-r-cyan-400 -left-2 top-1/2 transform -translate-y-1/2' :
            'border-r-transparent border-t-transparent border-b-transparent border-l-cyan-400 -right-2 top-1/2 transform -translate-y-1/2'
          }`}
        />
      </motion.div>

      {/* CSS for highlighting */}
      <style jsx global>{`
        .tutorial-highlight {
          position: relative;
          z-index: 45;
          box-shadow: 0 0 0 4px rgba(34, 211, 238, 0.5), 0 0 20px rgba(34, 211, 238, 0.3);
          border-radius: 8px;
          animation: tutorial-pulse 2s infinite;
        }
        
        @keyframes tutorial-pulse {
          0%, 100% { box-shadow: 0 0 0 4px rgba(34, 211, 238, 0.5), 0 0 20px rgba(34, 211, 238, 0.3); }
          50% { box-shadow: 0 0 0 8px rgba(34, 211, 238, 0.3), 0 0 30px rgba(34, 211, 238, 0.5); }
        }
      `}</style>
    </>
  )
}

export default InteractiveTutorial

"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Star, 
  Zap, 
  Trophy, 
  <PERSON>, 
  Sparkles, 
  ArrowUp,
  <PERSON>,
  Unlock
} from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  NanoCoreEvolutionStage, 
  NanoCoreCustomization,
  NANOCORE_EVOLUTION_STAGES 
} from '@/types/avatar'

interface NanoCoreEvolutionProps {
  currentStage: NanoCoreEvolutionStage
  customization: NanoCoreCustomization
  onEvolution: (newStage: NanoCoreEvolutionStage, updates: Partial<NanoCoreCustomization>) => void
  onUnlockComponent: (componentId: string) => void
  className?: string
}

interface EvolutionReward {
  type: 'component' | 'feature' | 'customization'
  id: string
  name: string
  description: string
  icon: React.ReactNode
}

export function NanoCoreEvolution({
  currentStage,
  customization,
  onEvolution,
  onUnlockComponent,
  className = ""
}: NanoCoreEvolutionProps) {
  const [showEvolutionAnimation, setShowEvolutionAnimation] = useState(false)
  const [pendingRewards, setPendingRewards] = useState<EvolutionReward[]>([])
  
  // Get current and next stage info
  const stageKeys = Object.keys(NANOCORE_EVOLUTION_STAGES) as (keyof typeof NANOCORE_EVOLUTION_STAGES)[]
  const currentStageIndex = stageKeys.indexOf(currentStage.stage)
  const nextStageKey = stageKeys[currentStageIndex + 1]
  const nextStage = nextStageKey ? NANOCORE_EVOLUTION_STAGES[nextStageKey] : null
  
  const currentStageInfo = NANOCORE_EVOLUTION_STAGES[currentStage.stage]
  
  // Calculate progress to next level
  const progressToNext = nextStage 
    ? Math.min((currentStage.xp / nextStage.xpRequired) * 100, 100)
    : 100
  
  // Check if ready to evolve
  const canEvolve = nextStage && currentStage.xp >= nextStage.xpRequired
  
  // Evolution rewards for each stage
  const getEvolutionRewards = (stage: string): EvolutionReward[] => {
    switch (stage) {
      case 'initiate':
        return [
          {
            type: 'component',
            id: 'crystal_core',
            name: 'Crystal Core',
            description: 'Unlock crystal core type',
            icon: <Sparkles className="w-4 h-4" />
          },
          {
            type: 'feature',
            id: 'particle_effects',
            name: 'Particle Effects',
            description: 'Enable particle animations',
            icon: <Zap className="w-4 h-4" />
          }
        ]
      case 'helper':
        return [
          {
            type: 'component',
            id: 'advanced_head',
            name: 'Advanced Head Unit',
            description: 'Unlock advanced head types',
            icon: <Crown className="w-4 h-4" />
          },
          {
            type: 'customization',
            id: 'color_variations',
            name: 'Color Variations',
            description: 'More color options',
            icon: <Star className="w-4 h-4" />
          }
        ]
      case 'mentor':
        return [
          {
            type: 'component',
            id: 'quantum_core',
            name: 'Quantum Core',
            description: 'Unlock quantum energy core',
            icon: <Zap className="w-4 h-4" />
          },
          {
            type: 'feature',
            id: 'holo_projections',
            name: 'Holo Projections',
            description: 'Project holographic displays',
            icon: <Sparkles className="w-4 h-4" />
          }
        ]
      case 'hero':
        return [
          {
            type: 'component',
            id: 'legendary_accessories',
            name: 'Legendary Gear',
            description: 'Unlock all legendary accessories',
            icon: <Trophy className="w-4 h-4" />
          },
          {
            type: 'feature',
            id: 'reality_bridge',
            name: 'Reality Bridge',
            description: 'Connect to real-world data',
            icon: <Crown className="w-4 h-4" />
          }
        ]
      default:
        return []
    }
  }
  
  const handleEvolution = () => {
    if (!canEvolve || !nextStage) return
    
    setShowEvolutionAnimation(true)
    
    // Create new evolution stage
    const newStage: NanoCoreEvolutionStage = {
      stage: nextStageKey,
      level: nextStage.level,
      xp: currentStage.xp,
      xpToNext: stageKeys[currentStageIndex + 2] 
        ? NANOCORE_EVOLUTION_STAGES[stageKeys[currentStageIndex + 2]].xpRequired - nextStage.xpRequired
        : 0,
      unlockedFeatures: [...currentStage.unlockedFeatures, ...nextStage.unlockedFeatures],
      visualUpgrades: nextStage.visualUpgrades
    }
    
    // Apply visual upgrades to customization
    const evolutionUpdates: Partial<NanoCoreCustomization> = {
      evolutionStage: newStage,
      coreGlow: Math.min(customization.coreGlow + 0.1, 1.0),
      energyCore: {
        ...customization.energyCore,
        intensity: Math.min(customization.energyCore.intensity + 0.1, 1.0),
        particles: newStage.visualUpgrades.particleEffects
      }
    }
    
    // Get rewards for this evolution
    const rewards = getEvolutionRewards(nextStageKey)
    setPendingRewards(rewards)
    
    // Trigger evolution
    setTimeout(() => {
      onEvolution(newStage, evolutionUpdates)
      setShowEvolutionAnimation(false)
    }, 2000)
  }
  
  const claimReward = (reward: EvolutionReward) => {
    if (reward.type === 'component') {
      onUnlockComponent(reward.id)
    }
    setPendingRewards(prev => prev.filter(r => r.id !== reward.id))
  }
  
  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'beginner': return <Zap className="w-5 h-5" />
      case 'initiate': return <Star className="w-5 h-5" />
      case 'helper': return <Sparkles className="w-5 h-5" />
      case 'mentor': return <Crown className="w-5 h-5" />
      case 'hero': return <Trophy className="w-5 h-5" />
      default: return <Zap className="w-5 h-5" />
    }
  }
  
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'beginner': return 'text-blue-400'
      case 'initiate': return 'text-green-400'
      case 'helper': return 'text-yellow-400'
      case 'mentor': return 'text-purple-400'
      case 'hero': return 'text-orange-400'
      default: return 'text-cyan-400'
    }
  }
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current Stage Display */}
      <Card className="bg-gray-900/95 border-gray-700">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-cyan-400">
            {getStageIcon(currentStage.stage)}
            Evolution Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Stage Info */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-bold ${getStageColor(currentStage.stage)}`}>
                {currentStageInfo.name}
              </h3>
              <p className="text-sm text-gray-400">{currentStageInfo.description}</p>
            </div>
            <Badge variant="outline" className="border-cyan-400 text-cyan-400">
              Level {currentStage.level}
            </Badge>
          </div>
          
          {/* XP Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Experience</span>
              <span className="text-cyan-400">
                {currentStage.xp} / {nextStage?.xpRequired || currentStage.xp} XP
              </span>
            </div>
            <Progress value={progressToNext} className="h-2" />
          </div>
          
          {/* Evolution Button */}
          {canEvolve && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="pt-2"
            >
              <Button
                onClick={handleEvolution}
                className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600"
                disabled={showEvolutionAnimation}
              >
                <ArrowUp className="w-4 h-4 mr-2" />
                Evolve to {nextStage?.name}
              </Button>
            </motion.div>
          )}
          
          {/* Next Stage Preview */}
          {nextStage && !canEvolve && (
            <div className="border-t border-gray-700 pt-4">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Next Evolution:</h4>
              <div className="flex items-center gap-2 text-sm text-gray-400">
                {getStageIcon(nextStageKey)}
                <span>{nextStage.name}</span>
                <span className="text-xs">
                  ({nextStage.xpRequired - currentStage.xp} XP needed)
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Unlocked Features */}
      <Card className="bg-gray-900/95 border-gray-700">
        <CardHeader className="pb-4">
          <CardTitle className="text-cyan-400 flex items-center gap-2">
            <Unlock className="w-5 h-5" />
            Unlocked Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {currentStage.unlockedFeatures.map((feature, index) => (
              <Badge
                key={index}
                variant="outline"
                className="border-green-400 text-green-400 justify-center"
              >
                {feature.replace('_', ' ')}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Evolution Animation Overlay */}
      <AnimatePresence>
        {showEvolutionAnimation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              className="text-center space-y-4"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-20 h-20 mx-auto border-4 border-cyan-400 border-t-transparent rounded-full"
              />
              <h2 className="text-2xl font-bold text-cyan-400">Evolution in Progress...</h2>
              <p className="text-gray-400">Your NanoCore is transforming!</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Pending Rewards */}
      <AnimatePresence>
        {pendingRewards.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-2"
          >
            {pendingRewards.map((reward) => (
              <Card key={reward.id} className="bg-gradient-to-r from-cyan-900/50 to-purple-900/50 border-cyan-400">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-cyan-400/20 rounded-lg">
                        {reward.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-cyan-400">{reward.name}</h4>
                        <p className="text-sm text-gray-400">{reward.description}</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => claimReward(reward)}
                      className="bg-cyan-500 hover:bg-cyan-600"
                    >
                      <Gift className="w-4 h-4 mr-1" />
                      Claim
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default NanoCoreEvolution

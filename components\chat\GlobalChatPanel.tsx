"use client"

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useChat } from '@/contexts/ChatContext'
import { useAuth } from '@/contexts/AuthContext'
import { ChatMessage } from './ChatMessage'
import { ChannelList } from './ChannelList'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  MessageCircle,
  X,
  Send,
  Users,
  Hash,
  Settings,
  Smile,
  Paperclip
} from 'lucide-react'

interface GlobalChatPanelProps {
  isOpen: boolean
  onToggle: () => void
  className?: string
}

export function GlobalChatPanel({ isOpen, onToggle, className = "" }: GlobalChatPanelProps) {
  const { user: _user } = useAuth()
  const {
    channels,
    directMessages,
    messages,
    activeChannelId,
    activeDMId,
    onlineUsers: _onlineUsers,
    notifications: _notifications,
    sendMessage,
    setActiveChannel: _setActiveChannel,
    setActiveDM: _setActiveDM
  } = useChat()

  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState<'channels' | 'dms' | 'users'>('channels')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, activeChannelId, activeDMId])

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isOpen])

  const handleSendMessage = async () => {
    if (!messageInput.trim()) return

    try {
      await sendMessage(messageInput, activeChannelId || undefined, activeDMId || undefined)
      setMessageInput('')
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const activeMessages = activeChannelId 
    ? messages[activeChannelId] || []
    : activeDMId 
    ? messages[activeDMId] || []
    : []

  const totalUnreadCount = channels.reduce((sum, channel) => sum + channel.unreadCount, 0) +
                          directMessages.reduce((sum, dm) => sum + dm.unreadCount, 0)

  return (
    <>
      {/* Chat Toggle Button */}
      <motion.div
        className="fixed right-4 top-1/2 transform -translate-y-1/2 z-50"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Button
          onClick={onToggle}
          size="lg"
          className="relative bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white shadow-lg rounded-full w-14 h-14 p-0"
        >
          <MessageCircle className="w-6 h-6" />
          {totalUnreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 rounded-full flex items-center justify-center">
              {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
            </Badge>
          )}
        </Button>
      </motion.div>

      {/* Chat Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className={`fixed right-0 top-0 h-full w-96 bg-black/95 backdrop-blur-xl border-l border-gray-800/50 z-40 flex flex-col ${className}`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-800/50">
              <div className="flex items-center gap-3">
                <MessageCircle className="w-5 h-5 text-cyan-400" />
                <h2 className="text-lg font-bold text-white font-space-grotesk">
                  NanoChat
                </h2>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white"
                >
                  <Settings className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggle}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>



            {/* Tabs */}
            <div className="flex border-b border-gray-800/50">
              {[
                { id: 'channels', label: 'Channels', icon: Hash },
                { id: 'dms', label: 'DMs', icon: MessageCircle },
                { id: 'users', label: 'Users', icon: Users }
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as any)}
                  className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
                    activeTab === id
                      ? 'text-cyan-400 border-b-2 border-cyan-400'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {label}
                </button>
              ))}
            </div>

            {/* Content Area */}
            <div className="flex-1 flex">
              {/* Sidebar */}
              <div className="w-1/3 border-r border-gray-800/50">
                <ChannelList
                  activeTab={activeTab}
                  searchQuery={searchQuery}
                  onSearchChange={setSearchQuery}
                />
              </div>

              {/* Chat Area */}
              <div className="flex-1 flex flex-col">
                {(activeChannelId || activeDMId) ? (
                  <>
                    {/* Messages */}
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-2">
                        {activeMessages.map((message) => (
                          <ChatMessage
                            key={message.id}
                            message={message}
                            onReact={(messageId, emoji) => {
                              // Handle reaction
                              console.log('React to message:', messageId, emoji)
                            }}
                            onReply={(messageId) => {
                              // Handle reply
                              console.log('Reply to message:', messageId)
                            }}
                            onEdit={(messageId, newContent) => {
                              // Handle edit
                              console.log('Edit message:', messageId, newContent)
                            }}
                            onDelete={(messageId) => {
                              // Handle delete
                              console.log('Delete message:', messageId)
                            }}
                          />
                        ))}
                        <div ref={messagesEndRef} />
                      </div>
                    </ScrollArea>

                    {/* Message Input */}
                    <div className="p-4 border-t border-gray-800/50">
                      <div className="flex gap-2">
                        <div className="flex-1 relative">
                          <Input
                            ref={inputRef}
                            placeholder="Type a message..."
                            value={messageInput}
                            onChange={(e) => setMessageInput(e.target.value)}
                            onKeyPress={handleKeyPress}
                            className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 pr-20"
                          />
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-white p-1 h-auto"
                            >
                              <Paperclip className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-white p-1 h-auto"
                            >
                              <Smile className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                        <Button
                          onClick={handleSendMessage}
                          disabled={!messageInput.trim()}
                          className="bg-cyan-500 hover:bg-cyan-600 text-white"
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex-1 flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Select a channel or start a conversation</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

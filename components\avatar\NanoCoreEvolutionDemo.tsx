"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, Pause, <PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON>teC<PERSON><PERSON>, Zap } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { NanoCoreVanilla } from './NanoCoreVanilla'
import { 
  NanoCoreCustomization, 
  DEFAULT_NANOCORE_CUSTOMIZATION 
} from '@/types/avatar'

export function NanoCoreEvolutionDemo() {
  const [currentStage, setCurrentStage] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [autoPlay, setAutoPlay] = useState(false)
  const [customGlow, setCustomGlow] = useState(0.8)
  const [customColor, setCustomColor] = useState('#00ffff')

  // Evolution stage configurations
  const evolutionStages = [
    {
      name: "Nano Spark",
      stage: "beginner",
      level: 1,
      description: "A newly awakened digital consciousness",
      customization: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION,
        coreType: 'sphere' as const,
        coreColor: '#00ffff',
        coreGlow: 0.3,
        headUnit: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
          type: 'basic' as const,
          size: 0.9
        },
        energyCore: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
          type: 'spark' as const,
          intensity: 0.4,
          particles: false
        },
        evolutionStage: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
          stage: 'beginner' as const,
          level: 1,
          visualUpgrades: {
            glowIntensity: 0.3,
            circuitComplexity: 1,
            particleEffects: false,
            advancedAnimations: false,
            customShapes: false
          }
        }
      }
    },
    {
      name: "Data Seeker",
      stage: "initiate",
      level: 5,
      description: "Growing in knowledge and capability",
      customization: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION,
        coreType: 'crystal' as const,
        coreColor: '#9400D3',
        coreGlow: 0.5,
        headUnit: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
          type: 'advanced' as const,
          size: 1.0
        },
        energyCore: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
          type: 'atom' as const,
          color: '#9400D3',
          intensity: 0.6,
          particles: true
        },
        evolutionStage: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
          stage: 'initiate' as const,
          level: 5,
          visualUpgrades: {
            glowIntensity: 0.5,
            circuitComplexity: 2,
            particleEffects: true,
            advancedAnimations: false,
            customShapes: false
          }
        }
      }
    },
    {
      name: "Code Companion",
      stage: "helper",
      level: 15,
      description: "A helpful guide in the digital realm",
      customization: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION,
        coreType: 'geometric' as const,
        coreColor: '#FFD700',
        coreGlow: 0.7,
        headUnit: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
          type: 'quantum' as const,
          size: 1.1
        },
        energyCore: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
          type: 'vortex' as const,
          color: '#FFD700',
          intensity: 0.8,
          particles: true
        },
        evolutionStage: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
          stage: 'helper' as const,
          level: 15,
          visualUpgrades: {
            glowIntensity: 0.7,
            circuitComplexity: 3,
            particleEffects: true,
            advancedAnimations: true,
            customShapes: false
          }
        }
      }
    },
    {
      name: "Wisdom Core",
      stage: "mentor",
      level: 30,
      description: "A knowledgeable mentor and teacher",
      customization: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION,
        coreType: 'organic' as const,
        coreColor: '#00FF7F',
        coreGlow: 0.9,
        headUnit: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
          type: 'neural' as const,
          size: 1.2
        },
        energyCore: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
          type: 'quantum' as const,
          color: '#00FF7F',
          intensity: 0.9,
          particles: true
        },
        evolutionStage: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
          stage: 'mentor' as const,
          level: 30,
          visualUpgrades: {
            glowIntensity: 0.9,
            circuitComplexity: 4,
            particleEffects: true,
            advancedAnimations: true,
            customShapes: true
          }
        }
      }
    },
    {
      name: "NanoHero",
      stage: "hero",
      level: 50,
      description: "The ultimate digital companion and guide",
      customization: {
        ...DEFAULT_NANOCORE_CUSTOMIZATION,
        coreType: 'crystal' as const,
        coreColor: '#FF6600',
        coreGlow: 1.0,
        headUnit: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.headUnit,
          type: 'neural' as const,
          size: 1.3
        },
        energyCore: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.energyCore,
          type: 'quantum' as const,
          color: '#FF6600',
          intensity: 1.0,
          particles: true
        },
        evolutionStage: {
          ...DEFAULT_NANOCORE_CUSTOMIZATION.evolutionStage,
          stage: 'hero' as const,
          level: 50,
          visualUpgrades: {
            glowIntensity: 1.0,
            circuitComplexity: 5,
            particleEffects: true,
            advancedAnimations: true,
            customShapes: true
          }
        }
      }
    }
  ]

  // Auto-play evolution
  React.useEffect(() => {
    if (!autoPlay) return

    const interval = setInterval(() => {
      setCurrentStage(prev => (prev + 1) % evolutionStages.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [autoPlay, evolutionStages.length])

  const nextStage = () => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentStage(prev => (prev + 1) % evolutionStages.length)
      setIsAnimating(false)
    }, 500)
  }

  const resetToBeginning = () => {
    setCurrentStage(0)
    setAutoPlay(false)
  }

  // Get current customization with user overrides
  const getCurrentCustomization = (): NanoCoreCustomization => {
    const base = evolutionStages[currentStage].customization
    return {
      ...base,
      coreGlow: customGlow,
      coreColor: customColor,
      energyCore: {
        ...base.energyCore,
        color: customColor,
        intensity: customGlow
      }
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'beginner': return 'text-blue-400 border-blue-400'
      case 'initiate': return 'text-purple-400 border-purple-400'
      case 'helper': return 'text-yellow-400 border-yellow-400'
      case 'mentor': return 'text-green-400 border-green-400'
      case 'hero': return 'text-orange-400 border-orange-400'
      default: return 'text-cyan-400 border-cyan-400'
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
      {/* 3D Preview */}
      <div className="space-y-4">
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center justify-between">
              <span>Evolution Showcase</span>
              <Badge variant="outline" className={getStageColor(evolutionStages[currentStage].stage)}>
                Level {evolutionStages[currentStage].level}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-96 lg:h-[500px] relative">
              <motion.div
                key={currentStage}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="w-full h-full"
              >
                <NanoCoreVanilla
                  customization={getCurrentCustomization()}
                  className="rounded-b-lg"
                />
              </motion.div>
              
              {/* Evolution effect overlay */}
              {isAnimating && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 backdrop-blur-sm rounded-b-lg flex items-center justify-center"
                >
                  <motion.div
                    animate={{ rotate: 360, scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.5 }}
                    className="text-4xl"
                  >
                    ⚡
                  </motion.div>
                </motion.div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Controls */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Evolution Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoPlay(!autoPlay)}
                className={autoPlay ? "bg-green-600 text-white" : ""}
              >
                {autoPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                {autoPlay ? 'Pause' : 'Auto Play'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={nextStage}
                disabled={autoPlay}
              >
                <SkipForward className="w-4 h-4" />
                Next Stage
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetToBeginning}
              >
                <RotateCcw className="w-4 h-4" />
                Reset
              </Button>
            </div>

            {/* Stage selector */}
            <div className="grid grid-cols-5 gap-1">
              {evolutionStages.map((stage, index) => (
                <Button
                  key={index}
                  variant={currentStage === index ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentStage(index)}
                  className="text-xs p-2"
                  disabled={autoPlay}
                >
                  {stage.level}
                </Button>
              ))}
            </div>

            {/* Custom controls */}
            <div className="space-y-3 border-t border-gray-700 pt-4">
              <div>
                <label className="text-sm text-gray-300 mb-2 block">
                  Glow Intensity: {Math.round(customGlow * 100)}%
                </label>
                <Slider
                  value={[customGlow]}
                  onValueChange={([value]) => setCustomGlow(value)}
                  max={1}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <div>
                <label className="text-sm text-gray-300 mb-2 block">Core Color</label>
                <div className="flex gap-2">
                  {['#00ffff', '#9400D3', '#FFD700', '#00FF7F', '#FF6600', '#FF1493'].map((color) => (
                    <button
                      key={color}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        customColor === color 
                          ? 'border-white scale-110' 
                          : 'border-gray-600 hover:border-gray-400'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setCustomColor(color)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stage Information */}
      <div className="space-y-4">
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className={`text-xl ${getStageColor(evolutionStages[currentStage].stage).split(' ')[0]}`}>
              {evolutionStages[currentStage].name}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-300">{evolutionStages[currentStage].description}</p>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Stage:</span>
                <span className="text-white ml-2 capitalize">{evolutionStages[currentStage].stage}</span>
              </div>
              <div>
                <span className="text-gray-400">Level:</span>
                <span className="text-white ml-2">{evolutionStages[currentStage].level}</span>
              </div>
              <div>
                <span className="text-gray-400">Core Type:</span>
                <span className="text-white ml-2 capitalize">{evolutionStages[currentStage].customization.coreType}</span>
              </div>
              <div>
                <span className="text-gray-400">Head Unit:</span>
                <span className="text-white ml-2 capitalize">{evolutionStages[currentStage].customization.headUnit.type}</span>
              </div>
            </div>

            {/* Visual Upgrades */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-cyan-400">Visual Upgrades</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className={`p-2 rounded ${evolutionStages[currentStage].customization.evolutionStage.visualUpgrades.particleEffects ? 'bg-green-900/30 text-green-400' : 'bg-gray-800 text-gray-500'}`}>
                  Particle Effects
                </div>
                <div className={`p-2 rounded ${evolutionStages[currentStage].customization.evolutionStage.visualUpgrades.advancedAnimations ? 'bg-green-900/30 text-green-400' : 'bg-gray-800 text-gray-500'}`}>
                  Advanced Animations
                </div>
                <div className={`p-2 rounded ${evolutionStages[currentStage].customization.evolutionStage.visualUpgrades.customShapes ? 'bg-green-900/30 text-green-400' : 'bg-gray-800 text-gray-500'}`}>
                  Custom Shapes
                </div>
                <div className={`p-2 rounded ${evolutionStages[currentStage].customization.evolutionStage.visualUpgrades.circuitComplexity > 2 ? 'bg-green-900/30 text-green-400' : 'bg-gray-800 text-gray-500'}`}>
                  Circuit Patterns
                </div>
              </div>
            </div>

            {/* Evolution Progress */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-cyan-400">Evolution Progress</h4>
              <div className="flex justify-between items-center">
                {evolutionStages.map((stage, index) => (
                  <div
                    key={index}
                    className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-bold transition-all ${
                      index <= currentStage
                        ? 'border-cyan-400 bg-cyan-400 text-black'
                        : 'border-gray-600 text-gray-400'
                    }`}
                  >
                    {stage.level}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Specs */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400">Technical Specifications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Rendering:</span>
              <span className="text-white">Three.js WebGL</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Polygons:</span>
              <span className="text-white">~2,000 optimized</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Materials:</span>
              <span className="text-white">PBR Standard</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Animations:</span>
              <span className="text-white">Real-time procedural</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Performance:</span>
              <span className="text-green-400">60 FPS target</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default NanoCoreEvolutionDemo

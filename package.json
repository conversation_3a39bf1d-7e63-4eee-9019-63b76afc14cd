{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/is-prop-valid": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/postcss": "^4.1.11", "@types/three": "^0.177.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.23", "css-minimizer-webpack-plugin": "^7.0.2", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "gsap": "^3.13.0", "input-otp": "1.4.1", "lottie-react": "^2.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-type-animation": "^3.2.0", "recharts": "2.15.0", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "vaul": "latest", "zustand": "^5.0.6"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "eslint-plugin-unused-imports": "^4.1.4", "glslify-loader": "^2.0.0", "postcss": "^8.5.6", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.17", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}
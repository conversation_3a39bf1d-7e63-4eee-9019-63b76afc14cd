"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowRight, 
  ArrowLeft,
  Globe, 
  Users, 
  Calendar, 
  Sparkles, 
  Heart,
  Brain,
  Hammer,
  Search,
  Trophy,
  Target,
  Building,
  CheckCircle,
  Play,
  Pause,
  Volume2,
  VolumeX
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'

interface OnboardingStep {
  id: string
  title: string
  subtitle: string
  content: React.ReactNode
  animation?: 'slide' | 'fade' | 'scale'
  duration?: number
  interactive?: boolean
  skipable?: boolean
}

interface EpochTownsOnboardingProps {
  isOpen: boolean
  onComplete: () => void
  onSkip?: () => void
  userProfile?: {
    name: string
    joinDate: Date
    interests: string[]
  }
  className?: string
}

export function EpochTownsOnboarding({
  isOpen,
  onComplete,
  onSkip,
  userProfile,
  className = ""
}: EpochTownsOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [hasSound, setHasSound] = useState(true)
  const [userChoices, setUserChoices] = useState<Record<string, any>>({})

  // Generate user's town generation based on join date
  const userGeneration = userProfile?.joinDate 
    ? userProfile.joinDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    : new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Epoch Towns!',
      subtitle: 'Where learning becomes a collaborative evolution',
      content: (
        <div className="text-center space-y-6">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="w-24 h-24 mx-auto bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full flex items-center justify-center"
          >
            <Globe className="w-12 h-12 text-white" />
          </motion.div>
          <div className="space-y-4">
            <p className="text-lg text-gray-300">
              {userProfile?.name ? `Hi ${userProfile.name}!` : 'Hello there!'} You&apos;re about to join something special.
            </p>
            <p className="text-gray-400">
              Epoch Towns is a collaborative learning platform where you&apos;ll join a monthly cohort,
              build together, and create a lasting legacy for future generations.
            </p>
          </div>
        </div>
      ),
      animation: 'fade',
      duration: 3000
    },
    {
      id: 'concept',
      title: 'The Concept',
      subtitle: 'Monthly cohorts that evolve together',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center p-4 bg-gray-800 rounded-lg"
            >
              <Calendar className="w-8 h-8 text-cyan-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">Monthly Cohorts</h3>
              <p className="text-sm text-gray-400">Join others who started in the same month</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-center p-4 bg-gray-800 rounded-lg"
            >
              <Users className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">Collaborate</h3>
              <p className="text-sm text-gray-400">Work together on shared goals</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="text-center p-4 bg-gray-800 rounded-lg"
            >
              <Sparkles className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">Evolve</h3>
              <p className="text-sm text-gray-400">Build a legacy for future generations</p>
            </motion.div>
          </div>
          <div className="text-center">
            <p className="text-gray-300">
              Your town: <span className="text-cyan-400 font-semibold">NanoVerse {userGeneration}</span>
            </p>
          </div>
        </div>
      ),
      animation: 'slide'
    },
    {
      id: 'dna',
      title: 'Town DNA System',
      subtitle: 'Every action shapes your town\'s genetic makeup',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <p className="text-gray-300 mb-4">
              Your town develops unique traits based on collective actions:
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <Heart className="w-6 h-6 text-red-400 mx-auto mb-2" />
              <h4 className="text-sm font-semibold text-white">Empathy Rich</h4>
              <p className="text-xs text-gray-400">Helping others</p>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <Brain className="w-6 h-6 text-blue-400 mx-auto mb-2" />
              <h4 className="text-sm font-semibold text-white">Ancient Wisdom</h4>
              <p className="text-xs text-gray-400">Sharing knowledge</p>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <Hammer className="w-6 h-6 text-yellow-400 mx-auto mb-2" />
              <h4 className="text-sm font-semibold text-white">Builder Generation</h4>
              <p className="text-xs text-gray-400">Creating things</p>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <Search className="w-6 h-6 text-green-400 mx-auto mb-2" />
              <h4 className="text-sm font-semibold text-white">Curious Minds</h4>
              <p className="text-xs text-gray-400">Asking questions</p>
            </div>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400">
              Strong traits become part of your town&apos;s DNA and get passed to future generations!
            </p>
          </div>
        </div>
      ),
      animation: 'scale'
    },
    {
      id: 'world',
      title: '3D Collaborative World',
      subtitle: 'Explore and build in your shared virtual space',
      content: (
        <div className="space-y-6">
          <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden">
            <motion.div
              animate={{ 
                rotateY: 360,
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                rotateY: { duration: 8, repeat: Infinity, ease: "linear" },
                scale: { duration: 2, repeat: Infinity }
              }}
              className="w-32 h-32 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
            >
              <Building className="w-16 h-16 text-white" />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <p className="text-white text-sm font-medium">Your 3D Town Preview</p>
              <p className="text-gray-300 text-xs">Minecraft-style collaborative building</p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-white">What you can do:</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Explore the 3D world
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Build structures together
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Unlock new areas
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Meet other citizens
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-white">Buildings unlock:</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>🏛️ Wisdom Library</li>
                <li>❤️ Empathy Garden</li>
                <li>🔨 Builder Workshop</li>
                <li>🔬 Curiosity Lab</li>
              </ul>
            </div>
          </div>
        </div>
      ),
      animation: 'slide'
    },
    {
      id: 'contribution',
      title: 'How to Contribute',
      subtitle: 'Every action matters and shapes your town',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-white">Contribution Types:</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                  <Brain className="w-5 h-5 text-blue-400 mt-0.5" />
                  <div>
                    <h5 className="text-sm font-medium text-white">Share Wisdom</h5>
                    <p className="text-xs text-gray-400">Post tutorials, answer questions</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                  <Heart className="w-5 h-5 text-red-400 mt-0.5" />
                  <div>
                    <h5 className="text-sm font-medium text-white">Show Empathy</h5>
                    <p className="text-xs text-gray-400">Help others, give encouragement</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                  <Hammer className="w-5 h-5 text-yellow-400 mt-0.5" />
                  <div>
                    <h5 className="text-sm font-medium text-white">Build Things</h5>
                    <p className="text-xs text-gray-400">Create projects, add structures</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                  <Search className="w-5 h-5 text-green-400 mt-0.5" />
                  <div>
                    <h5 className="text-sm font-medium text-white">Spark Curiosity</h5>
                    <p className="text-xs text-gray-400">Ask questions, explore ideas</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold text-white">Impact:</h4>
              <div className="space-y-3">
                <div className="p-3 bg-gradient-to-r from-cyan-900/50 to-purple-900/50 rounded-lg border border-cyan-600/30">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="w-4 h-4 text-cyan-400" />
                    <span className="text-sm font-medium text-white">Immediate</span>
                  </div>
                  <p className="text-xs text-gray-300">Earn XP, unlock features, help others</p>
                </div>
                <div className="p-3 bg-gradient-to-r from-green-900/50 to-blue-900/50 rounded-lg border border-green-600/30">
                  <div className="flex items-center gap-2 mb-2">
                    <Trophy className="w-4 h-4 text-green-400" />
                    <span className="text-sm font-medium text-white">Town Level</span>
                  </div>
                  <p className="text-xs text-gray-300">Strengthen town traits, unlock areas</p>
                </div>
                <div className="p-3 bg-gradient-to-r from-purple-900/50 to-pink-900/50 rounded-lg border border-purple-600/30">
                  <div className="flex items-center gap-2 mb-2">
                    <Sparkles className="w-4 h-4 text-purple-400" />
                    <span className="text-sm font-medium text-white">Legacy</span>
                  </div>
                  <p className="text-xs text-gray-300">Shape DNA for future generations</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
      animation: 'slide'
    },
    {
      id: 'personalization',
      title: 'Personalize Your Experience',
      subtitle: 'Tell us about your interests to get started',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <p className="text-gray-300">
              What are you most interested in? (Select all that apply)
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {[
              { id: 'coding', label: 'Coding', icon: '💻' },
              { id: 'ai', label: 'AI/ML', icon: '🤖' },
              { id: 'design', label: 'Design', icon: '🎨' },
              { id: 'gaming', label: 'Gaming', icon: '🎮' },
              { id: 'music', label: 'Music', icon: '🎵' },
              { id: 'science', label: 'Science', icon: '🔬' },
              { id: 'writing', label: 'Writing', icon: '✍️' },
              { id: 'math', label: 'Math', icon: '📊' },
              { id: 'art', label: 'Art', icon: '🖼️' }
            ].map((interest) => (
              <motion.button
                key={interest.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  const current = userChoices.interests || []
                  const updated = current.includes(interest.id)
                    ? current.filter((i: string) => i !== interest.id)
                    : [...current, interest.id]
                  setUserChoices({ ...userChoices, interests: updated })
                }}
                className={`p-3 rounded-lg border transition-all ${
                  userChoices.interests?.includes(interest.id)
                    ? 'bg-cyan-600 border-cyan-400 text-white'
                    : 'bg-gray-800 border-gray-600 text-gray-300 hover:border-gray-500'
                }`}
              >
                <div className="text-2xl mb-1">{interest.icon}</div>
                <div className="text-sm font-medium">{interest.label}</div>
              </motion.button>
            ))}
          </div>
        </div>
      ),
      animation: 'fade',
      interactive: true
    },
    {
      id: 'ready',
      title: 'You\'re Ready!',
      subtitle: 'Welcome to your collaborative learning journey',
      content: (
        <div className="text-center space-y-6">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-20 h-20 mx-auto bg-gradient-to-r from-green-500 to-cyan-500 rounded-full flex items-center justify-center"
          >
            <CheckCircle className="w-10 h-10 text-white" />
          </motion.div>
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-white">
              Welcome to NanoVerse {userGeneration}!
            </h3>
            <p className="text-gray-300">
              You&apos;re now part of a collaborative community that will evolve together.
            </p>
            <div className="p-4 bg-gradient-to-r from-cyan-900/50 to-purple-900/50 rounded-lg border border-cyan-600/30">
              <h4 className="font-semibold text-white mb-2">Your next steps:</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>✨ Explore your 3D town</li>
                <li>👋 Meet other citizens</li>
                <li>🎯 Make your first contribution</li>
                <li>🧬 Watch your town&apos;s DNA evolve</li>
              </ul>
            </div>
          </div>
        </div>
      ),
      animation: 'scale'
    }
  ]

  const currentStepData = steps[currentStep]
  const progress = ((currentStep + 1) / steps.length) * 100

  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }, [currentStep, steps.length, onComplete])

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const skipOnboarding = () => {
    if (onSkip) {
      onSkip()
    } else {
      onComplete()
    }
  }

  // Auto-advance for non-interactive steps
  useEffect(() => {
    if (!currentStepData.interactive && isPlaying && currentStepData.duration) {
      const timer = setTimeout(() => {
        nextStep()
      }, currentStepData.duration)
      return () => clearTimeout(timer)
    }
  }, [currentStep, isPlaying, currentStepData, nextStep])

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className={`bg-gray-900 rounded-xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-hidden ${className}`}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Epoch Towns Onboarding</h1>
                <p className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="w-8 h-8 p-0"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setHasSound(!hasSound)}
                className="w-8 h-8 p-0"
              >
                {hasSound ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={skipOnboarding}
              >
                Skip
              </Button>
            </div>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Content */}
        <div className="p-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ 
                opacity: 0, 
                x: currentStepData.animation === 'slide' ? 50 : 0,
                scale: currentStepData.animation === 'scale' ? 0.9 : 1
              }}
              animate={{ 
                opacity: 1, 
                x: 0,
                scale: 1
              }}
              exit={{ 
                opacity: 0, 
                x: currentStepData.animation === 'slide' ? -50 : 0,
                scale: currentStepData.animation === 'scale' ? 0.9 : 1
              }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-white">{currentStepData.title}</h2>
                <p className="text-gray-400">{currentStepData.subtitle}</p>
              </div>
              
              <div className="max-w-3xl mx-auto">
                {currentStepData.content}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Previous
            </Button>
            
            <div className="flex items-center gap-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentStep 
                      ? 'bg-cyan-400' 
                      : index < currentStep 
                        ? 'bg-green-400' 
                        : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>

            <Button
              onClick={nextStep}
              className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 flex items-center gap-2"
            >
              {currentStep === steps.length - 1 ? 'Get Started' : 'Next'}
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default EpochTownsOnboarding

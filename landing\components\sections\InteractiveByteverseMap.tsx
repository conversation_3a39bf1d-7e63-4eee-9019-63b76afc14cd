"use client"

import React, { useState, useRef, Suspense, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Float } from '@react-three/drei'
import * as THREE from 'three'
import {
  Shield,
  Gamepad2,
  Eye,
  ChevronRight,
  Zap,
  Sparkles,
  Brain,
  Globe,
  Atom
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface InteractiveByteverseMapProps {
  onSignupClick: () => void
}

// Enhanced Quantum Realm Component
function QuantumRealm({ position, color, isActive, onClick, children, realmType: _realmType }: {
  position: [number, number, number]
  color: string
  isActive: boolean
  onClick: () => void
  children: React.ReactNode
  realmType: string
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const glowRef = useRef<THREE.Mesh>(null)
  const particlesRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (!meshRef.current || !glowRef.current) return

    const time = state.clock.getElapsedTime()

    // Quantum realm rotation and floating
    meshRef.current.rotation.y = time * 0.4
    meshRef.current.rotation.x = Math.sin(time * 0.2) * 0.1
    meshRef.current.position.y = position[1] + Math.sin(time * 1.5 + position[0]) * 0.4

    // Quantum pulsing effect
    if (isActive) {
      const pulse = 1.4 + Math.sin(time * 4) * 0.2
      meshRef.current.scale.setScalar(pulse)
      glowRef.current.scale.setScalar(pulse * 1.3)
    } else {
      const breathe = 1 + Math.sin(time * 2) * 0.08
      meshRef.current.scale.setScalar(breathe)
      glowRef.current.scale.setScalar(breathe * 1.2)
    }

    // Quantum glow rotation
    glowRef.current.rotation.y = -time * 0.5
    glowRef.current.rotation.z = time * 0.3

    // Particle system rotation
    if (particlesRef.current) {
      particlesRef.current.rotation.y = time * 0.2
    }
  })

  return (
    <Float speed={2} rotationIntensity={0.4} floatIntensity={0.4}>
      <group onClick={onClick}>
        {/* Quantum glow effect */}
        <mesh ref={glowRef}>
          <cylinderGeometry args={[2.5, 2.5, 0.2, 8]} />
          <meshStandardMaterial
            color={color}
            emissive={color}
            emissiveIntensity={isActive ? 0.8 : 0.3}
            transparent
            opacity={0.4}
          />
        </mesh>

        {/* Main quantum realm */}
        <mesh ref={meshRef} position={position}>
          <cylinderGeometry args={[1.5, 1.5, 0.6, 8]} />
          <meshStandardMaterial
            color={color}
            emissive={color}
            emissiveIntensity={isActive ? 1.2 : 0.5}
            transparent
            opacity={0.9}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>

        {/* Quantum core */}
        <mesh>
          <sphereGeometry args={[0.4, 16, 16]} />
          <meshStandardMaterial
            color="#ffffff"
            emissive={color}
            emissiveIntensity={isActive ? 2.0 : 1.0}
            transparent
            opacity={0.9}
          />
        </mesh>

        {/* Quantum particle system */}
        <group ref={particlesRef}>
          {[...Array(6)].map((_, i) => (
            <mesh key={i} position={[
              Math.cos((i / 6) * Math.PI * 2) * 2,
              Math.sin(i * 0.5) * 0.5,
              Math.sin((i / 6) * Math.PI * 2) * 2
            ]}>
              <sphereGeometry args={[0.1, 8, 8]} />
              <meshStandardMaterial
                color={color}
                emissive={color}
                emissiveIntensity={0.8}
                transparent
                opacity={0.7}
              />
            </mesh>
          ))}
        </group>

        {children}
      </group>
    </Float>
  )
}

// Enhanced Quantum NanoVerse Scene
function QuantumNanoVerseScene({ activeRealm, onRealmClick }: {
  activeRealm: string | null
  onRealmClick: (realm: string) => void
}) {
  const quantumRealms = [
    { id: 'tutorial', position: [-4, 0, -2] as [number, number, number], color: '#22d3ee', type: 'neural' },
    { id: 'hacklab', position: [4, 0, -2] as [number, number, number], color: '#8b5cf6', type: 'quantum' },
    { id: 'arcade', position: [-4, 0, 2] as [number, number, number], color: '#a855f7', type: 'gaming' },
    { id: 'studio', position: [4, 0, 2] as [number, number, number], color: '#fbbf24', type: 'creative' },
  ]

  const centralHubRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (!centralHubRef.current) return

    const time = state.clock.getElapsedTime()
    centralHubRef.current.rotation.y = time * 0.3
    centralHubRef.current.position.y = Math.sin(time * 2) * 0.2

    // Quantum pulsing
    const pulse = 1 + Math.sin(time * 3) * 0.1
    centralHubRef.current.scale.setScalar(pulse)
  })

  return (
    <>
      {/* Enhanced quantum lighting */}
      <ambientLight intensity={0.3} />
      <pointLight position={[10, 10, 10]} intensity={1.5} color="#22d3ee" />
      <pointLight position={[-10, -10, -10]} intensity={0.8} color="#8b5cf6" />
      <pointLight position={[0, 15, 0]} intensity={1} color="#fbbf24" />

      {/* Quantum Central Nexus */}
      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.3}>
        <group>
          {/* Main nexus core */}
          <mesh ref={centralHubRef} position={[0, 0, 0]}>
            <cylinderGeometry args={[2, 2, 0.5, 12]} />
            <meshStandardMaterial
              color="#22d3ee"
              emissive="#22d3ee"
              emissiveIntensity={0.8}
              transparent
              opacity={0.9}
              metalness={0.9}
              roughness={0.1}
            />
          </mesh>

          {/* Quantum nexus glow */}
          <mesh position={[0, 0, 0]}>
            <cylinderGeometry args={[2.5, 2.5, 0.2, 12]} />
            <meshStandardMaterial
              color="#22d3ee"
              emissive="#22d3ee"
              emissiveIntensity={0.5}
              transparent
              opacity={0.4}
            />
          </mesh>

          {/* Central quantum core */}
          <mesh position={[0, 0.3, 0]}>
            <sphereGeometry args={[0.6, 20, 20]} />
            <meshStandardMaterial
              color="#ffffff"
              emissive="#22d3ee"
              emissiveIntensity={1.5}
              transparent
              opacity={0.8}
            />
          </mesh>

          {/* Orbiting quantum particles */}
          {[...Array(8)].map((_, i) => (
            <mesh key={i} position={[
              Math.cos((i / 8) * Math.PI * 2) * 3,
              Math.sin(i * 0.3) * 0.5,
              Math.sin((i / 8) * Math.PI * 2) * 3
            ]}>
              <sphereGeometry args={[0.15, 12, 12]} />
              <meshStandardMaterial
                color="#fbbf24"
                emissive="#fbbf24"
                emissiveIntensity={1.0}
                transparent
                opacity={0.8}
              />
            </mesh>
          ))}
        </group>
      </Float>

      {/* Quantum Realms */}
      {quantumRealms.map((realm) => (
        <QuantumRealm
          key={realm.id}
          position={realm.position}
          color={realm.color}
          isActive={activeRealm === realm.id}
          onClick={() => onRealmClick(realm.id)}
          realmType={realm.type}
        >
          {/* Quantum connection conduits */}
          <mesh position={[realm.position[0] * -0.25, 0, realm.position[2] * -0.25]}>
            <cylinderGeometry args={[0.05, 0.05, 3.5, 12]} />
            <meshStandardMaterial
              color="#22d3ee"
              emissive="#22d3ee"
              emissiveIntensity={0.6}
              transparent
              opacity={0.7}
            />
          </mesh>

          {/* Quantum energy flow */}
          <mesh position={[realm.position[0] * -0.15, 0.2, realm.position[2] * -0.15]}>
            <cylinderGeometry args={[0.02, 0.02, 2.5, 8]} />
            <meshStandardMaterial
              color={realm.color}
              emissive={realm.color}
              emissiveIntensity={0.8}
              transparent
              opacity={0.8}
            />
          </mesh>
        </QuantumRealm>
      ))}

      <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.3} />
    </>
  )
}

export function InteractiveByteverseMap({ onSignupClick }: InteractiveByteverseMapProps) {
  const [activeRealm, setActiveRealm] = useState<string | null>(null)
  const [selectedRealm, setSelectedRealm] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum particle system for background
  const quantumParticles = Array.from({ length: 25 }, (_, i) => ({
    left: (i * 17 + 3) % 100,
    top: (i * 23 + 7) % 100,
    delay: i * 0.2,
    color: i % 4 === 0 ? '#22d3ee' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#fbbf24' : '#a855f7'
  }))

  // Enhanced quantum realms with neural terminology
  const quantumRealms = [
    {
      id: 'tutorial',
      title: 'Neural Learning Nexus',
      icon: Brain,
      quantumColor: '#22d3ee',
      color: 'from-neural-cyan to-cyan-400',
      description: 'Master quantum fundamentals through neural-enhanced interactive consciousness lessons',
      features: ['500+ Neural Tutorials', 'Consciousness-Friendly', 'Quantum Progress Tracking'],
      preview: 'Initialize your first quantum algorithm and build neural consciousness apps',
      realm: 'Neural Domain'
    },
    {
      id: 'hacklab',
      title: 'Quantum Security Matrix',
      icon: Shield,
      quantumColor: '#8b5cf6',
      color: 'from-quantum-purple to-purple-400',
      description: 'Explore quantum cryptography through consciousness security challenges and neural CTFs',
      features: ['Quantum CTF Events', 'Neural Security Simulations', 'Consciousness Hacking'],
      preview: 'Decrypt quantum codes, discover neural vulnerabilities, and become a consciousness security expert',
      realm: 'Quantum Security Realm'
    },
    {
      id: 'arcade',
      title: 'Consciousness Gaming Sphere',
      icon: Gamepad2,
      quantumColor: '#a855f7',
      color: 'from-purple-500 to-pink-500',
      description: 'Neural gaming meets consciousness learning with quantum tournaments and neural mod creation',
      features: ['Quantum Gaming Tournaments', 'Neural Game Reviews', 'Consciousness Mod Development'],
      preview: 'Compete in neural tournaments and create quantum game consciousness modifications',
      realm: 'Gaming Consciousness Realm'
    },
    {
      id: 'studio',
      title: 'Quantum Creation Lab',
      icon: Sparkles,
      quantumColor: '#fbbf24',
      color: 'from-quantum-gold to-yellow-400',
      description: 'Create, synthesize, and showcase quantum projects with the neural consciousness community',
      features: ['Quantum Project Showcase', 'Neural Content Creation', 'Consciousness Community Feedback'],
      preview: 'Build quantum consciousness projects and share them with fellow NanoHeroes',
      realm: 'Creative Quantum Realm'
    }
  ]

  const handleRealmClick = (realmId: string) => {
    setActiveRealm(realmId)
    setSelectedRealm(realmId)
  }

  const getRealmData = (id: string) => {
    return quantumRealms.find(r => r.id === id)
  }

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum NanoVerse background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* Quantum field particles */}
        {isClient && quantumParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: 360
            }}
            transition={{
              duration: 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum realm glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-40 h-40 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-quantum-gold/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/3 w-36 h-36 bg-purple-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Explore the Quantum
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              NanoVerse
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            A glimpse into our immersive <span className="text-neural-cyan font-semibold">3D quantum consciousness world</span>.
            Four specialized <span className="text-quantum-purple font-semibold">neural realms</span> where learning transcends reality
            through <span className="text-quantum-gold font-semibold">interactive quantum experiences</span> and consciousness collaboration.
          </motion.p>

          {/* Quantum exploration decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Globe className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        {/* 3D Quantum NanoVerse Interactive Map */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-[28rem] lg:h-[32rem] mb-16 relative"
        >
          {/* Quantum field background for 3D canvas */}
          <div className="absolute inset-0 rounded-3xl border border-neural-cyan/20 quantum-glass">
            <div className="absolute inset-0 bg-gradient-to-r from-neural-cyan/5 via-quantum-purple/5 to-quantum-gold/5 rounded-3xl" />
          </div>

          <Canvas
            camera={{ position: [0, 10, 10], fov: 75 }}
            gl={{ antialias: true, alpha: true, powerPreference: 'high-performance' }}
            style={{ borderRadius: '1.5rem' }}
          >
            <Suspense fallback={null}>
              <QuantumNanoVerseScene
                activeRealm={activeRealm}
                onRealmClick={handleRealmClick}
              />
            </Suspense>
          </Canvas>

          {/* Enhanced quantum interaction hint */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2.5 }}
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          >
            <div className="quantum-glass rounded-xl px-4 py-2 border border-neural-cyan/30">
              <p className="text-neural-cyan text-sm font-medium font-space-grotesk flex items-center gap-2">
                <Atom className="w-4 h-4" />
                Click quantum realms to explore • Drag to navigate consciousness
                <Sparkles className="w-4 h-4" />
              </p>
            </div>
          </motion.div>
        </motion.div>

        {/* Quantum Realm Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {quantumRealms.map((realm, index) => (
            <motion.div
              key={realm.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.15 }}
              whileHover={{ scale: 1.03, y: -8 }}
              className={`group cursor-pointer ${
                selectedRealm === realm.id ? `ring-2` : ''
              }`}
              style={{
                '--ring-color': selectedRealm === realm.id ? realm.quantumColor : 'transparent'
              } as React.CSSProperties}
              onClick={() => handleRealmClick(realm.id)}
            >
              <Card
                className="quantum-glass border-2 h-full relative overflow-hidden group-hover:border-opacity-60 transition-all duration-500"
                style={{
                  borderColor: `${realm.quantumColor}40`,
                  background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                  boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${realm.quantumColor}20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                }}
              >
                {/* Quantum glow effect on hover */}
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                  style={{
                    background: `linear-gradient(135deg, ${realm.quantumColor}30, ${realm.quantumColor}10)`,
                    boxShadow: `0 0 40px ${realm.quantumColor}40`
                  }}
                />

                {/* Quantum realm particles */}
                {isClient && (
                  <div className="absolute inset-0 overflow-hidden rounded-xl">
                    {[...Array(4)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 rounded-full"
                        style={{
                          left: `${20 + i * 20}%`,
                          top: `${20 + i * 15}%`,
                          backgroundColor: realm.quantumColor
                        }}
                        animate={{
                          opacity: [0, 0.8, 0],
                          scale: [0.5, 1.2, 0.5],
                          y: [0, -10, 0]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Number.POSITIVE_INFINITY,
                          delay: i * 0.5
                        }}
                      />
                    ))}
                  </div>
                )}

                <CardContent className="p-6 relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <motion.div
                      className="relative p-3 rounded-xl border-2 group-hover:scale-110 transition-transform duration-300"
                      style={{
                        background: `linear-gradient(135deg, ${realm.quantumColor}20, ${realm.quantumColor}10)`,
                        borderColor: realm.quantumColor,
                        boxShadow: `0 0 20px ${realm.quantumColor}40`
                      }}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      <realm.icon className="w-5 h-5 text-white" />

                      {/* Icon quantum glow */}
                      <div
                        className="absolute inset-0 rounded-xl blur-lg opacity-50"
                        style={{ backgroundColor: realm.quantumColor }}
                      />
                    </motion.div>
                    <div>
                      <h3
                        className="text-lg font-bold text-white group-hover:text-opacity-90 transition-all duration-300 font-orbitron"
                        style={{
                          textShadow: `0 0 20px ${realm.quantumColor}60`
                        }}
                      >
                        {realm.title}
                      </h3>
                      <div className="flex items-center gap-1 text-xs text-white/60 font-space-grotesk">
                        <Atom className="w-3 h-3" style={{ color: realm.quantumColor }} />
                        <span>{realm.realm}</span>
                      </div>
                    </div>
                  </div>

                  <p className="text-white/80 mb-4 text-sm leading-relaxed font-space-grotesk">
                    {realm.description}
                  </p>

                  <div className="space-y-2 mb-4">
                    {realm.features.map((feature, i) => (
                      <div key={i} className="flex items-center gap-2 text-xs text-white/70">
                        <div
                          className="w-1 h-1 rounded-full"
                          style={{ backgroundColor: realm.quantumColor }}
                        />
                        {feature}
                      </div>
                    ))}
                  </div>

                  <motion.div
                    className="flex items-center group-hover:translate-x-2 transition-all duration-300"
                    style={{ color: realm.quantumColor }}
                    whileHover={{ x: 5 }}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium font-space-grotesk">Explore Quantum Realm</span>
                    <ChevronRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Selected Quantum Realm Preview */}
        {selectedRealm && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="quantum-glass rounded-3xl p-8 lg:p-12 border-2 mb-12 relative overflow-hidden"
            style={{
              borderColor: `${getRealmData(selectedRealm)?.quantumColor}40`,
              background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
              boxShadow: `0 0 40px ${getRealmData(selectedRealm)?.quantumColor}20`
            }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-20" />
            {isClient && (
              <div className="absolute inset-0 overflow-hidden rounded-3xl">
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 rounded-full"
                    style={{
                      left: `${10 + i * 10}%`,
                      top: `${20 + (i % 3) * 20}%`,
                      backgroundColor: getRealmData(selectedRealm)?.quantumColor
                    }}
                    animate={{
                      opacity: [0, 0.8, 0],
                      scale: [0.5, 1.5, 0.5],
                      rotate: 360
                    }}
                    transition={{
                      duration: 4,
                      repeat: Number.POSITIVE_INFINITY,
                      delay: i * 0.3
                    }}
                  />
                ))}
              </div>
            )}

            {(() => {
              const realm = getRealmData(selectedRealm)
              if (!realm) return null

              return (
                <div className="text-center relative z-10">
                  <div className="flex items-center justify-center gap-4 mb-6">
                    <motion.div
                      className="relative p-4 rounded-xl border-2"
                      style={{
                        background: `linear-gradient(135deg, ${realm.quantumColor}20, ${realm.quantumColor}10)`,
                        borderColor: realm.quantumColor,
                        boxShadow: `0 0 30px ${realm.quantumColor}40`
                      }}
                      animate={{
                        scale: [1, 1.1, 1],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Number.POSITIVE_INFINITY
                      }}
                    >
                      <realm.icon className="w-8 h-8 text-white" />

                      {/* Enhanced icon glow */}
                      <div
                        className="absolute inset-0 rounded-xl blur-xl opacity-60"
                        style={{ backgroundColor: realm.quantumColor }}
                      />
                    </motion.div>
                    <h3
                      className="text-2xl lg:text-3xl font-bold text-white font-orbitron"
                      style={{ textShadow: `0 0 30px ${realm.quantumColor}60` }}
                    >
                      {realm.title}
                    </h3>
                  </div>
                  <p className="text-white/80 mb-8 max-w-3xl mx-auto italic text-lg lg:text-xl font-space-grotesk leading-relaxed">
                    &ldquo;{realm.preview}&rdquo;
                  </p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={onSignupClick}
                      variant="quantum"
                      size="xl"
                      className="relative font-orbitron"
                      style={{
                        background: `linear-gradient(135deg, ${realm.quantumColor}, ${realm.quantumColor}80)`,
                        borderColor: `${realm.quantumColor}60`,
                        boxShadow: `0 0 30px ${realm.quantumColor}40`
                      }}
                    >
                      <Zap className="w-6 h-6 mr-3" />
                      Enter {realm.title}
                      <Sparkles className="w-6 h-6 ml-3" />
                    </Button>
                  </motion.div>
                </div>
              )
            })()}
          </motion.div>
        )}

        {/* Quantum NanoVerse CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <motion.div
            className="quantum-glass rounded-3xl p-8 lg:p-12 border-2 relative overflow-hidden"
            style={{
              borderColor: '#22d3ee40',
              background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
              boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
            }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-neural-cyan/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-40 h-40 bg-quantum-purple/20 rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-quantum-gold/20 rounded-full blur-3xl" />

            {/* Quantum exploration particles */}
            {isClient && quantumParticles.slice(0, 10).map((particle, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full"
                style={{
                  left: `${particle.left}%`,
                  top: `${particle.top}%`,
                  backgroundColor: particle.color
                }}
                animate={{
                  opacity: [0, 0.8, 0],
                  scale: [0.5, 1.2, 0.5],
                  rotate: 360
                }}
                transition={{
                  duration: 4,
                  repeat: Number.POSITIVE_INFINITY,
                  delay: particle.delay
                }}
              />
            ))}

            <div className="relative z-10">
              <motion.h3
                className="text-2xl lg:text-3xl font-bold text-white mb-4 font-orbitron"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
              >
                <span className="text-neural-cyan">🚀</span> Ready to Explore the Full{' '}
                <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
                  Quantum NanoVerse
                </span>?
              </motion.h3>

              <p className="text-base sm:text-lg lg:text-xl text-white/80 mb-8 lg:mb-10 max-w-4xl mx-auto font-space-grotesk leading-relaxed">
                This is just a glimpse of our immersive <span className="text-neural-cyan font-semibold">3D consciousness world</span>.
                Join <span className="text-quantum-purple font-semibold">NanoHero</span> to unlock all quantum realms,
                participate in <span className="text-quantum-gold font-semibold">neural community events</span>, and shape the future of consciousness learning.
              </p>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                {/* Enhanced quantum glow effect */}
                <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

                <Button
                  onClick={onSignupClick}
                  variant="quantum"
                  size="xl"
                  className="relative bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-2xl hover:shadow-neural-cyan/50 transition-all duration-500 border-2 border-neural-cyan/40 font-orbitron"
                >
                  <Globe className="w-6 h-6 mr-3" />
                  Enter the Quantum NanoVerse
                  <Atom className="w-6 h-6 ml-3" />
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

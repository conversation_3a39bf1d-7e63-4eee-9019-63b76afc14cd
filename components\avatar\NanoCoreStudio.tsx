"use client"

import React, { useState, useCallback } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Set<PERSON>s, 
  <PERSON><PERSON><PERSON>, 
  RotateCcw,
  Save,
  Download,
  Upload,
  Play,
  Volume2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { NanoCore } from './NanoCore'
import { NanoCoreCustomizer } from './NanoCoreCustomizer'
import { NanoCoreEvolution } from './NanoCoreEvolution'
import { 
  NanoCoreCustomization, 
  Avatar3DSettings,
  NanoCoreEvolutionStage,
  NanoCoreAIPersonality,
  DEFAULT_NANOCORE_CUSTOMIZATION,
  DEFAULT_AVATAR_SETTINGS,
  DEFAULT_NANOCORE_AI_PERSONALITY,
  NANOCORE_ANIMATIONS
} from '@/types/avatar'

interface NanoCoreStudioProps {
  initialCustomization?: NanoCoreCustomization
  initialSettings?: Avatar3DSettings
  initialPersonality?: NanoCoreAIPersonality
  onSave?: (data: {
    customization: NanoCoreCustomization
    settings: Avatar3DSettings
    personality: NanoCoreAIPersonality
  }) => void
  className?: string
}

export function NanoCoreStudio({
  initialCustomization = DEFAULT_NANOCORE_CUSTOMIZATION,
  initialSettings = DEFAULT_AVATAR_SETTINGS,
  initialPersonality = DEFAULT_NANOCORE_AI_PERSONALITY,
  onSave,
  className = ""
}: NanoCoreStudioProps) {
  const [customization, setCustomization] = useState<NanoCoreCustomization>(initialCustomization)
  const [settings, setSettings] = useState<Avatar3DSettings>(initialSettings)
  const [personality, setPersonality] = useState<NanoCoreAIPersonality>(initialPersonality)
  const [activeTab, setActiveTab] = useState("customize")
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentAnimation, setCurrentAnimation] = useState("idle")
  
  const updateCustomization = useCallback((updates: Partial<NanoCoreCustomization>) => {
    setCustomization(prev => ({ ...prev, ...updates }))
  }, [])
  
  const updateSettings = useCallback((updates: Partial<Avatar3DSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }))
  }, [])
  
  const updatePersonality = useCallback((updates: Partial<NanoCoreAIPersonality>) => {
    setPersonality(prev => ({ ...prev, ...updates }))
  }, [])
  
  const handleEvolution = (newStage: NanoCoreEvolutionStage, updates: Partial<NanoCoreCustomization>) => {
    updateCustomization(updates)
  }
  
  const handleUnlockComponent = (componentId: string) => {
    updateCustomization({
      unlockedComponents: [...customization.unlockedComponents, componentId]
    })
  }
  
  const handleSave = () => {
    onSave?.({
      customization,
      settings,
      personality
    })
  }
  
  const handleExport = () => {
    const data = {
      customization,
      settings,
      personality,
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `nanocore-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        if (data.customization) setCustomization(data.customization)
        if (data.settings) setSettings(data.settings)
        if (data.personality) setPersonality(data.personality)
      } catch (error) {
        console.error('Failed to import NanoCore data:', error)
      }
    }
    reader.readAsText(file)
  }
  
  const playAnimation = (animationType: keyof typeof NANOCORE_ANIMATIONS) => {
    setCurrentAnimation(animationType)
    setIsPlaying(true)
    
    // Auto-stop after animation duration
    setTimeout(() => {
      setIsPlaying(false)
      setCurrentAnimation("idle")
    }, 3000)
  }
  
  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 h-full ${className}`}>
      {/* 3D Preview Panel */}
      <div className="space-y-4">
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                NanoCore Preview
              </span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateSettings({ autoRotate: !settings.autoRotate })}
                  className="border-gray-600"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  className="border-cyan-600 text-cyan-400"
                >
                  <Save className="w-4 h-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-96 lg:h-[500px]">
              <NanoCore
                customization={customization}
                settings={settings}
                aiPersonality={personality}
                interactionMode="showcase"
                className="rounded-b-lg"
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Animation Controls */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center gap-2">
              <Play className="w-5 h-5" />
              Animation Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-2">
              {Object.keys(NANOCORE_ANIMATIONS).map((animType) => (
                <Button
                  key={animType}
                  variant={currentAnimation === animType ? "default" : "outline"}
                  size="sm"
                  onClick={() => playAnimation(animType as keyof typeof NANOCORE_ANIMATIONS)}
                  className="text-xs"
                  disabled={isPlaying}
                >
                  {animType}
                </Button>
              ))}
            </div>
            
            <Separator />
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.enableAnimations}
                  onCheckedChange={(checked) => updateSettings({ enableAnimations: checked })}
                />
                <Label className="text-sm text-gray-300">Enable Animations</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-gray-400" />
                <Switch
                  checked={false}
                  onCheckedChange={() => {}}
                />
                <Label className="text-sm text-gray-300">Sound</Label>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Export/Import */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardContent className="p-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="flex-1 border-gray-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('import-input')?.click()}
                className="flex-1 border-gray-600"
              >
                <Upload className="w-4 h-4 mr-2" />
                Import
              </Button>
              <input
                id="import-input"
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Customization Panel */}
      <div className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-gray-800">
            <TabsTrigger value="customize" className="text-xs">
              <Palette className="w-4 h-4 mr-1" />
              Customize
            </TabsTrigger>
            <TabsTrigger value="evolution" className="text-xs">
              <Sparkles className="w-4 h-4 mr-1" />
              Evolution
            </TabsTrigger>
            <TabsTrigger value="settings" className="text-xs">
              <Settings className="w-4 h-4 mr-1" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="customize" className="space-y-4">
            <NanoCoreCustomizer
              customization={customization}
              onCustomizationChange={updateCustomization}
              onSavePreset={(name, description) => {
                console.log('Save preset:', name, description)
              }}
            />
          </TabsContent>

          <TabsContent value="evolution" className="space-y-4">
            <NanoCoreEvolution
              currentStage={customization.evolutionStage}
              customization={customization}
              onEvolution={handleEvolution}
              onUnlockComponent={handleUnlockComponent}
            />
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card className="bg-gray-900/95 border-gray-700">
              <CardHeader className="pb-4">
                <CardTitle className="text-cyan-400 flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Rendering Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm text-gray-300">Quality</Label>
                    <select
                      value={settings.quality}
                      onChange={(e) => updateSettings({ quality: e.target.value as any })}
                      className="bg-gray-800 border border-gray-600 rounded px-2 py-1 text-sm"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm text-gray-300">Auto Rotate</Label>
                    <Switch
                      checked={settings.autoRotate}
                      onCheckedChange={(checked) => updateSettings({ autoRotate: checked })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm text-gray-300">Physics</Label>
                    <Switch
                      checked={settings.enablePhysics}
                      onCheckedChange={(checked) => updateSettings({ enablePhysics: checked })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm text-gray-300">Level of Detail</Label>
                    <Switch
                      checked={settings.enableLOD}
                      onCheckedChange={(checked) => updateSettings({ enableLOD: checked })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gray-900/95 border-gray-700">
              <CardHeader className="pb-4">
                <CardTitle className="text-cyan-400 flex items-center gap-2">
                  <Volume2 className="w-5 h-5" />
                  AI Personality
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm text-gray-300">Name</Label>
                    <input
                      type="text"
                      value={personality.name}
                      onChange={(e) => updatePersonality({ name: e.target.value })}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-sm mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label className="text-sm text-gray-300">Voice Style</Label>
                    <select
                      value={personality.voice}
                      onChange={(e) => updatePersonality({ voice: e.target.value as any })}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-sm mt-1"
                    >
                      <option value="synthetic">Synthetic</option>
                      <option value="warm">Warm</option>
                      <option value="energetic">Energetic</option>
                      <option value="wise">Wise</option>
                      <option value="playful">Playful</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label className="text-sm text-gray-300">Response Style</Label>
                    <select
                      value={personality.responseStyle}
                      onChange={(e) => updatePersonality({ responseStyle: e.target.value as any })}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-sm mt-1"
                    >
                      <option value="concise">Concise</option>
                      <option value="detailed">Detailed</option>
                      <option value="encouraging">Encouraging</option>
                      <option value="challenging">Challenging</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default NanoCoreStudio

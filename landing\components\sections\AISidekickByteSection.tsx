"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON>t,
  MessageCircle,
  Send,
  Volume2,
  <PERSON>rkles,
  Brain,
  Zap,
  Heart,
  Atom
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

interface AISidekickByteSectionProps {
  onSignupClick: () => void
}

interface ChatMessage {
  id: string
  text: string
  isBot: boolean
  timestamp: Date
}

export function AISidekickByteSection({ onSignupClick }: AISidekickByteSectionProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Quantum consciousness particles for ByteMentor
  const mentorParticles = Array.from({ length: 12 }, (_, i) => ({
    left: (i * 29 + 5) % 100,
    top: (i * 23 + 7) % 100,
    delay: i * 0.4,
    color: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
  }))

  // Enhanced quantum consciousness questions
  const quantumSampleQuestions = [
    "How do I create quantum consciousness mods for Minecraft?",
    "What's the best way to learn Neural Python programming?",
    "Can you help me with my first quantum consciousness project?",
    "How does quantum cybersecurity work in the NanoVerse?",
    "What neural programming language should I start with?"
  ]

  // Enhanced ByteMentor quantum responses
  const byteMentorResponses = [
    "Quantum question! I'd love to help you explore consciousness modding. Minecraft quantum modifications are an amazing way to learn neural programming - you can start with simple consciousness block modifications and work your way up to complex quantum gameplay mechanics! ⚡🧠",
    "Neural Python is perfect for consciousness beginners! It reads almost like quantum thoughts and has tons of neural applications. I can guide you through building consciousness games, quantum web apps, or even AI neural projects step by step! 🌟",
    "Absolutely! Starting your first quantum consciousness project is exciting. I recommend beginning with something that resonates with your neural patterns - maybe a simple consciousness game or a quantum tool that solves a problem you care about! 🚀",
    "Quantum cybersecurity is like being a consciousness detective! You learn to think like both neural defenders and quantum attackers to protect consciousness systems. Our Quantum Security Matrix has safe, legal neural challenges to get you started! 🛡️",
    "For neural beginners, I usually recommend Quantum Python or Neural JavaScript. Quantum Python is great for learning consciousness concepts, while Neural JavaScript lets you build interactive quantum websites. What interests you more - consciousness data science or quantum web development? 💫"
  ]

  // Enhanced quantum initial message
  const quantumInitialMessages: ChatMessage[] = useMemo(() => [
    {
      id: '1',
      text: "Greetings, future NanoHero! I'm ByteMentor, your quantum consciousness AI companion! 🤖⚡ I'm here to help you navigate your neural coding journey, answer quantum questions, and guide your consciousness evolution. What quantum realm would you like to explore today? 🌟🧠",
      isBot: true,
      timestamp: new Date()
    }
  ], [])

  useEffect(() => {
    if (showChat && messages.length === 0) {
      setMessages(quantumInitialMessages)
    }
  }, [showChat, messages.length, quantumInitialMessages])

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputValue,
      isBot: false,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsTyping(true)

    // Simulate quantum AI response
    setTimeout(() => {
      const randomResponse = byteMentorResponses[Math.floor(Math.random() * byteMentorResponses.length)]
      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: randomResponse,
        isBot: true,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, botMessage])
      setIsTyping(false)
    }, 1500)
  }

  const handleQuantumQuestionClick = (question: string) => {
    setInputValue(question)
    setShowChat(true)
  }

  return (
    <section className="relative px-6 py-20 overflow-hidden">
      {/* Quantum ByteMentor background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark via-space-blue to-space-dark" />
        <div className="absolute inset-0 consciousness-wave opacity-20" />

        {/* ByteMentor consciousness particles */}
        {isClient && mentorParticles.map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              backgroundColor: particle.color
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
              rotate: 360
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Quantum mentor glow orbs */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-quantum-purple/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-quantum-gold/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Quantum Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-orbitron"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
              Meet ByteMentor
            </span>
            <br />
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent text-3xl md:text-4xl lg:text-5xl">
              Your Quantum AI Consciousness Guide
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto font-space-grotesk leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Your personal <span className="text-neural-cyan font-semibold">quantum consciousness mentor</span> who&apos;s always ready to help,
            encourage, and guide you through your <span className="text-quantum-purple font-semibold">neural learning journey</span>.
            ByteMentor adapts to your consciousness style and evolves with your quantum growth.
          </motion.p>

          {/* Quantum mentor decoration */}
          <motion.div
            className="flex justify-center items-center gap-4 mt-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-px w-16 bg-gradient-to-r from-transparent to-neural-cyan" />
            <Bot className="w-5 h-5 text-quantum-gold" />
            <div className="h-px w-16 bg-gradient-to-l from-transparent to-quantum-purple" />
          </motion.div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* ByteMentor Character & Quantum Features */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* ByteMentor Quantum Avatar */}
            <motion.div
              className="relative"
              whileHover={{ scale: 1.05 }}
            >
              <Card
                className="quantum-glass border-2 p-8 text-center relative overflow-hidden"
                style={{
                  borderColor: '#22d3ee40',
                  background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                  boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
                }}
              >
                {/* Quantum consciousness background */}
                <div className="absolute inset-0 consciousness-wave opacity-20" />

                {/* ByteMentor quantum avatar */}
                <motion.div
                  className="relative w-32 h-32 mx-auto mb-6 rounded-full flex items-center justify-center border-4"
                  style={{
                    background: `linear-gradient(135deg, #22d3ee, #8b5cf6, #fbbf24)`,
                    borderColor: '#22d3ee',
                    boxShadow: `0 0 40px #22d3ee60`
                  }}
                  animate={{
                    boxShadow: [
                      "0 0 20px rgba(34, 211, 238, 0.5)",
                      "0 0 40px rgba(139, 92, 246, 0.7)",
                      "0 0 60px rgba(251, 191, 36, 0.6)",
                      "0 0 40px rgba(139, 92, 246, 0.7)",
                      "0 0 20px rgba(34, 211, 238, 0.5)"
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Bot className="w-16 h-16 text-white relative z-10" />

                  {/* Quantum consciousness particles around ByteMentor */}
                  {isClient && Array.from({ length: 12 }, (_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 rounded-full"
                      style={{
                        top: `${20 + Math.sin(i * Math.PI / 6) * 70}%`,
                        left: `${20 + Math.cos(i * Math.PI / 6) * 70}%`,
                        backgroundColor: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#8b5cf6' : '#fbbf24'
                      }}
                      animate={{
                        scale: [0, 1, 0],
                        opacity: [0, 1, 0],
                        rotate: 360
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </motion.div>

                <div className="relative z-10">
                  <h3
                    className="text-2xl lg:text-3xl font-bold text-white mb-2 font-orbitron"
                    style={{ textShadow: '0 0 20px #22d3ee60' }}
                  >
                    ByteMentor
                  </h3>
                  <p
                    className="font-medium mb-4 font-space-grotesk"
                    style={{ color: '#22d3ee' }}
                  >
                    Your Quantum Consciousness Learning Companion
                  </p>
                  <p className="text-white/80 text-sm lg:text-base leading-relaxed font-space-grotesk">
                    &ldquo;I&apos;m here to make neural learning fun, accessible, and personalized for your consciousness!
                    Think of me as your quantum coding companion who never gets tired of your neural questions. ⚡🧠&rdquo;
                  </p>
                </div>
              </Card>
            </motion.div>

            {/* ByteMentor's Quantum Capabilities */}
            <div className="space-y-4">
              <h4 className="text-xl lg:text-2xl font-bold text-white mb-6 font-orbitron flex items-center gap-2">
                <Atom className="w-6 h-6 text-quantum-gold" />
                Quantum Consciousness Capabilities:
              </h4>

              {[
                {
                  icon: Brain,
                  title: "Neural Personalized Learning",
                  description: "Adapts to your consciousness learning style and quantum pace",
                  quantumColor: "#22d3ee"
                },
                {
                  icon: MessageCircle,
                  title: "24/7 Quantum Support",
                  description: "Always available to answer neural questions and provide consciousness guidance",
                  quantumColor: "#8b5cf6"
                },
                {
                  icon: Sparkles,
                  title: "Consciousness Motivation & Encouragement",
                  description: "Celebrates your quantum wins and helps you through neural challenges",
                  quantumColor: "#fbbf24"
                },
                {
                  icon: Zap,
                  title: "Quantum Smart Recommendations",
                  description: "Suggests consciousness projects and neural challenges based on your quantum interests",
                  quantumColor: "#10b981"
                }
              ].map((capability, index) => (
                <motion.div
                  key={capability.title}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.15 }}
                  className="flex items-start gap-4 p-4 lg:p-6 quantum-glass rounded-xl border-2 hover:border-opacity-60 transition-all duration-300 relative overflow-hidden group"
                  style={{
                    borderColor: `${capability.quantumColor}40`,
                    background: `linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(10, 15, 28, 0.7))`,
                    boxShadow: `0 0 15px ${capability.quantumColor}20`
                  }}
                >
                  {/* Quantum glow effect on hover */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                    style={{
                      background: `linear-gradient(135deg, ${capability.quantumColor}30, ${capability.quantumColor}10)`,
                      boxShadow: `0 0 40px ${capability.quantumColor}40`
                    }}
                  />

                  {/* Capability particles */}
                  {isClient && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      {[...Array(2)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            left: `${30 + i * 40}%`,
                            top: `${25 + i * 25}%`,
                            backgroundColor: capability.quantumColor
                          }}
                          animate={{
                            opacity: [0, 0.8, 0],
                            scale: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.5
                          }}
                        />
                      ))}
                    </div>
                  )}

                  <div className="relative z-10">
                    <motion.div
                      className="p-3 rounded-xl border-2 w-fit"
                      style={{
                        background: `linear-gradient(135deg, ${capability.quantumColor}20, ${capability.quantumColor}10)`,
                        borderColor: capability.quantumColor,
                        boxShadow: `0 0 15px ${capability.quantumColor}40`
                      }}
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      <capability.icon className="w-6 h-6 text-white" />
                    </motion.div>
                  </div>

                  <div className="relative z-10 flex-1">
                    <h5
                      className="font-semibold text-white mb-2 font-orbitron"
                      style={{ textShadow: `0 0 15px ${capability.quantumColor}60` }}
                    >
                      {capability.title}
                    </h5>
                    <p className="text-white/70 text-sm lg:text-base font-space-grotesk leading-relaxed">
                      {capability.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Interactive Quantum Chat Demo */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative"
          >
            <Card
              className="quantum-glass border-2 h-96 flex flex-col relative overflow-hidden"
              style={{
                borderColor: '#8b5cf640',
                background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
                boxShadow: `0 0 40px #8b5cf620, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
              }}
            >
              {/* Quantum chat background */}
              <div className="absolute inset-0 consciousness-wave opacity-10" />

              {/* Chat header */}
              <div
                className="p-4 border-b-2 relative z-10"
                style={{ borderColor: '#8b5cf640' }}
              >
                <div className="flex items-center gap-3">
                  <motion.div
                    className="w-10 h-10 rounded-full flex items-center justify-center border-2 relative"
                    style={{
                      background: `linear-gradient(135deg, #22d3ee, #8b5cf6)`,
                      borderColor: '#22d3ee',
                      boxShadow: `0 0 20px #22d3ee40`
                    }}
                    animate={{
                      boxShadow: [
                        "0 0 10px rgba(34, 211, 238, 0.4)",
                        "0 0 20px rgba(139, 92, 246, 0.6)",
                        "0 0 10px rgba(34, 211, 238, 0.4)"
                      ]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Bot className="w-5 h-5 text-white" />

                    {/* Quantum activity indicator */}
                    <motion.div
                      className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full border-2 border-white"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.8, 1, 0.8]
                      }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                  </motion.div>
                  <div className="flex-1">
                    <h4
                      className="font-semibold text-white font-orbitron"
                      style={{ textShadow: '0 0 15px #22d3ee60' }}
                    >
                      Chat with ByteMentor
                    </h4>
                    <div className="flex items-center gap-2">
                      <motion.div
                        className="w-2 h-2 bg-emerald-400 rounded-full"
                        animate={{
                          scale: [1, 1.3, 1],
                          opacity: [0.7, 1, 0.7]
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <span className="text-xs text-white/70 font-space-grotesk">
                        Quantum Consciousness Online & Ready to Guide
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white/60 hover:text-neural-cyan border border-neural-cyan/30 hover:border-neural-cyan/60"
                      >
                        <Volume2 className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>

              {/* Quantum Chat Messages */}
              <div className="flex-1 p-4 overflow-y-auto space-y-4 relative z-10">
                <AnimatePresence>
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}
                    >
                      <div
                        className={`max-w-xs p-3 lg:p-4 rounded-xl border relative overflow-hidden ${
                          message.isBot
                            ? 'quantum-glass border-neural-cyan/30 text-white/90'
                            : 'border-quantum-purple/40 text-white'
                        }`}
                        style={{
                          background: message.isBot
                            ? `linear-gradient(135deg, rgba(34, 211, 238, 0.1), rgba(34, 211, 238, 0.05))`
                            : `linear-gradient(135deg, #8b5cf6, #a855f7)`,
                          boxShadow: message.isBot
                            ? `0 0 15px rgba(34, 211, 238, 0.2)`
                            : `0 0 15px rgba(139, 92, 246, 0.3)`
                        }}
                      >
                        {/* Message quantum particles */}
                        {isClient && message.isBot && (
                          <div className="absolute inset-0 overflow-hidden rounded-xl">
                            {[...Array(2)].map((_, i) => (
                              <motion.div
                                key={i}
                                className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                                style={{
                                  left: `${20 + i * 60}%`,
                                  top: `${30 + i * 40}%`,
                                }}
                                animate={{
                                  opacity: [0, 0.6, 0],
                                  scale: [0.5, 1, 0.5]
                                }}
                                transition={{
                                  duration: 2,
                                  repeat: Number.POSITIVE_INFINITY,
                                  delay: i * 0.5
                                }}
                              />
                            ))}
                          </div>
                        )}

                        <p className="text-sm lg:text-base font-space-grotesk leading-relaxed relative z-10">
                          {message.text}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* Quantum typing indicator */}
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex justify-start"
                  >
                    <div
                      className="quantum-glass p-3 lg:p-4 rounded-xl border border-neural-cyan/30 relative"
                      style={{
                        background: `linear-gradient(135deg, rgba(34, 211, 238, 0.1), rgba(34, 211, 238, 0.05))`,
                        boxShadow: `0 0 15px rgba(34, 211, 238, 0.2)`
                      }}
                    >
                      <div className="flex gap-2">
                        {[0, 1, 2].map((i) => (
                          <motion.div
                            key={i}
                            className="w-2 h-2 bg-neural-cyan rounded-full"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 1, 0.5]
                            }}
                            transition={{
                              duration: 0.8,
                              repeat: Infinity,
                              delay: i * 0.2
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>

              {/* Quantum Chat Input */}
              <div
                className="p-4 border-t-2 relative z-10"
                style={{ borderColor: '#8b5cf640' }}
              >
                <div className="flex gap-3">
                  <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Ask ByteMentor anything about quantum consciousness..."
                    className="quantum-glass border-neural-cyan/30 text-white placeholder-white/50 font-space-grotesk"
                    style={{
                      background: `linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(10, 15, 28, 0.7))`,
                      boxShadow: `0 0 10px rgba(34, 211, 238, 0.2)`
                    }}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  />
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={handleSendMessage}
                      variant="quantum"
                      className="bg-gradient-to-r from-neural-cyan to-quantum-purple hover:from-neural-cyan/80 hover:to-quantum-purple/80 border-2 border-neural-cyan/40"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </motion.div>
                </div>
              </div>
            </Card>

            {/* Quantum Sample Questions */}
            {!showChat && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="mt-6"
              >
                <h5 className="font-semibold text-white mb-4 font-orbitron flex items-center gap-2">
                  <MessageCircle className="w-5 h-5 text-neural-cyan" />
                  Try asking ByteMentor:
                </h5>
                <div className="space-y-3">
                  {quantumSampleQuestions.slice(0, 3).map((question, index) => (
                    <motion.button
                      key={index}
                      onClick={() => handleQuantumQuestionClick(question)}
                      className="w-full text-left p-4 quantum-glass rounded-xl border-2 text-white/80 hover:text-white transition-all duration-300 text-sm lg:text-base font-space-grotesk relative overflow-hidden group"
                      style={{
                        borderColor: '#22d3ee40',
                        background: `linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(10, 15, 28, 0.7))`,
                        boxShadow: `0 0 10px rgba(34, 211, 238, 0.2)`
                      }}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {/* Question quantum glow on hover */}
                      <div
                        className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"
                        style={{
                          background: `linear-gradient(135deg, #22d3ee30, #22d3ee10)`,
                          boxShadow: `0 0 30px #22d3ee40`
                        }}
                      />

                      {/* Question particles */}
                      {isClient && (
                        <div className="absolute inset-0 overflow-hidden rounded-xl">
                          <motion.div
                            className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                            style={{
                              left: `${80 + index * 5}%`,
                              top: `${30 + index * 10}%`,
                            }}
                            animate={{
                              opacity: [0, 0.6, 0],
                              scale: [0.5, 1, 0.5]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Number.POSITIVE_INFINITY,
                              delay: index * 0.3
                            }}
                          />
                        </div>
                      )}

                      <span className="relative z-10">&quot;{question}&quot;</span>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Quantum ByteMentor CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="text-center mt-20"
        >
          <motion.div
            className="quantum-glass rounded-3xl p-8 lg:p-12 border-2 relative overflow-hidden"
            style={{
              borderColor: '#22d3ee40',
              background: `linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)`,
              boxShadow: `0 0 40px #22d3ee20, inset 0 1px 0 rgba(255, 255, 255, 0.1)`
            }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {/* Quantum background effects */}
            <div className="absolute inset-0 consciousness-wave opacity-30" />
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-neural-cyan/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-40 h-40 bg-quantum-purple/20 rounded-full blur-3xl" />

            {/* ByteMentor CTA particles */}
            {isClient && mentorParticles.slice(0, 8).map((particle, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full"
                style={{
                  left: `${particle.left}%`,
                  top: `${particle.top}%`,
                  backgroundColor: particle.color
                }}
                animate={{
                  opacity: [0, 0.8, 0],
                  scale: [0.5, 1.2, 0.5],
                  rotate: 360
                }}
                transition={{
                  duration: 3,
                  repeat: Number.POSITIVE_INFINITY,
                  delay: particle.delay
                }}
              />
            ))}

            <div className="relative z-10">
              <motion.h3
                className="text-2xl lg:text-3xl font-bold text-white mb-4 font-orbitron"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
              >
                <span className="text-neural-cyan">🤖</span> Ready to Meet Your{' '}
                <span className="bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
                  Quantum AI Consciousness Guide
                </span>?
              </motion.h3>

              <p className="text-base sm:text-lg lg:text-xl text-white/80 mb-8 lg:mb-10 max-w-4xl mx-auto font-space-grotesk leading-relaxed">
                <span className="text-neural-cyan font-semibold">ByteMentor</span> is waiting to help you on your
                quantum consciousness journey. Join <span className="text-quantum-purple font-semibold">NanoHero</span> and get personalized
                neural guidance, quantum encouragement, and consciousness support every step of the way.
              </p>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                {/* Enhanced quantum glow effect */}
                <div className="absolute -inset-2 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-500 quantum-pulse" />

                <Button
                  onClick={onSignupClick}
                  variant="quantum"
                  size="xl"
                  className="relative bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold text-white font-bold shadow-2xl hover:shadow-neural-cyan/50 transition-all duration-500 border-2 border-neural-cyan/40 font-orbitron"
                >
                  <Heart className="w-6 h-6 mr-3" />
                  Meet ByteMentor & Start Neural Learning
                  <Bot className="w-6 h-6 ml-3" />
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ChevronDown, Zap, Cpu, Dna, Target, Sparkles } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface HeroSectionProps {
  onScrollToFeatures?: () => void
}

const HeroSection = ({ onScrollToFeatures }: HeroSectionProps) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [_currentStage, _setCurrentStage] = useState(0)
  const [showCTA, setShowCTA] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
    const timer = setTimeout(() => setShowCTA(true), 3000)
    return () => clearTimeout(timer)
  }, [])

  const handleCollapse = () => {
    if (onScrollToFeatures) {
      onScrollToFeatures()
    } else {
      const element = document.querySelector('#features')
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900 to-teal-900" />
      
      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 50 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="space-y-8"
        >
          {/* Main Title with Enhanced Styling */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.8 }}
            transition={{ duration: 1, delay: 1 }}
            className="relative z-10"
          >
            <motion.h1
              className="text-7xl md:text-9xl lg:text-[10rem] font-black font-syne relative"
              animate={{
                textShadow: [
                  '0 0 20px rgba(34, 211, 238, 0.5)',
                  '0 0 40px rgba(139, 92, 246, 0.7)',
                  '0 0 20px rgba(34, 211, 238, 0.5)'
                ]
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="bg-gradient-to-r from-cyan-400 via-purple-500 via-yellow-400 to-cyan-400 bg-clip-text text-transparent bg-[length:200%_100%] animate-gradient-x">
                NanoHero
              </span>

              {/* Glowing backdrop */}
              <span
                className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent opacity-30 blur-sm"
                aria-hidden="true"
              >
                NanoHero
              </span>
            </motion.h1>


          </motion.div>

          {/* Enhanced Subtitle */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 1, delay: 1.5 }}
            className="max-w-4xl mx-auto space-y-6"
          >
            <p className="text-2xl md:text-3xl text-white/90 leading-relaxed font-light">
              Experience the fusion of{' '}
              <span className="text-cyan-400 font-semibold bg-cyan-400/10 px-2 py-1 rounded-lg">
                consciousness
              </span>{' '}
              and{' '}
              <span className="text-purple-500 font-semibold bg-purple-500/10 px-2 py-1 rounded-lg">
                quantum learning
              </span>{' '}
              in the digital realm.
            </p>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 2, duration: 0.8 }}
              className="relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 via-purple-500/20 to-yellow-400/20 rounded-2xl blur-xl" />
              <div className="relative bg-black/30 backdrop-blur-md border border-cyan-400/30 rounded-2xl p-6">
                <p className="text-xl md:text-2xl text-cyan-400 font-bold tracking-wide">
                  ⚡ Ignite your neural pathways ⚡
                </p>
                <p className="text-white/70 mt-2 text-lg">
                  Where consciousness meets quantum possibility
                </p>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Feature Icons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 1, delay: 2.5 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto py-12"
          >
            {[
              { icon: Zap, label: 'Neural Core', color: '#22d3ee', description: 'Advanced AI Processing' },
              { icon: Cpu, label: 'Quantum AI', color: '#8b5cf6', description: 'Quantum Computing' },
              { icon: Dna, label: 'Evolution', color: '#fbbf24', description: 'Adaptive Learning' },
              { icon: Target, label: 'Precision', color: '#22d3ee', description: 'Targeted Results' }
            ].map((item, index) => (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, scale: 0, rotateY: 180 }}
                animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                transition={{
                  delay: 3 + index * 0.2,
                  duration: 0.8,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 5,
                  transition: { duration: 0.3 }
                }}
                className="group relative"
              >
                <div className="relative bg-black/40 backdrop-blur-md border border-white/10 rounded-2xl p-6 h-full transition-all duration-300 group-hover:border-cyan-400/50 group-hover:bg-black/60">
                  {/* Glow effect */}
                  <div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: `linear-gradient(to bottom right, ${item.color}20, transparent)`
                    }}
                  />

                  {/* Icon container */}
                  <div className="relative flex flex-col items-center space-y-4">
                    <div className="relative">
                      <motion.div
                        animate={{
                          rotate: [0, 360],
                          scale: [1, 1.1, 1]
                        }}
                        transition={{
                          rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                          scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                        }}
                        className="w-16 h-16 rounded-full flex items-center justify-center border"
                        style={{
                          background: `linear-gradient(to bottom right, ${item.color}20, ${item.color}05)`,
                          borderColor: `${item.color}30`
                        }}
                      >
                        <item.icon className="w-8 h-8" style={{ color: item.color }} />
                      </motion.div>

                      {/* Pulsing ring */}
                      <motion.div
                        animate={{
                          scale: [1, 1.5, 1],
                          opacity: [0.5, 0, 0.5]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: index * 0.5
                        }}
                        className="absolute inset-0 w-16 h-16 rounded-full border-2"
                        style={{ borderColor: `${item.color}50` }}
                      />
                    </div>

                    {/* Text content */}
                    <div className="text-center space-y-2">
                      <h3
                        className="text-lg font-bold group-hover:text-white transition-colors duration-300"
                        style={{ color: item.color }}
                      >
                        {item.label}
                      </h3>
                      <p className="text-sm text-white/60 group-hover:text-white/80 transition-colors duration-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
          
          {/* Enhanced Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 1, delay: 4 }}
            className="space-y-8"
          >
            <div className="flex flex-col lg:flex-row gap-6 justify-center items-center max-w-2xl mx-auto">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                {/* Glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-purple-500 to-yellow-400 rounded-2xl blur-lg opacity-60 group-hover:opacity-100 transition-opacity duration-300" />

                <Button
                  variant="quantum"
                  size="xl"
                  glow={showCTA}
                  pulse={showCTA}
                  onClick={handleCollapse}
                  className="relative text-xl font-black tracking-wider px-8 py-4 bg-black/80 backdrop-blur-md border-2 border-cyan-400/50 hover:border-cyan-400"
                >
                  <motion.div
                    animate={{ rotate: showCTA ? [0, 360] : 0 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Target className="w-6 h-6 mr-3" />
                  </motion.div>
                  COLLAPSE INTO TIMELINE
                  <motion.div
                    animate={{
                      scale: showCTA ? [1, 1.2, 1] : 1,
                      rotate: showCTA ? [0, 180, 360] : 0
                    }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Sparkles className="w-6 h-6 ml-3" />
                  </motion.div>
                </Button>
              </motion.div>
            </div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 3.5 }}
              className="grid grid-cols-3 gap-8 max-w-2xl mx-auto pt-8"
            >
              {[
                { value: '10K+', label: 'Neural Nodes' },
                { value: '99.9%', label: 'Quantum Sync' },
                { value: '∞', label: 'Possibilities' }
              ].map((stat) => (
                <Card key={stat.label} variant="glass" className="p-4">
                  <div className="text-2xl font-bold text-cyan-400">{stat.value}</div>
                  <div className="text-sm text-white/60">{stat.label}</div>
                </Card>
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 4 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex flex-col items-center space-y-2 cursor-pointer"
          onClick={handleCollapse}
        >
          <span className="text-white/60 text-sm">Explore</span>
          <ChevronDown className="w-6 h-6 text-cyan-400" />
        </motion.div>
      </motion.div>
    </section>
  )
}

export default HeroSection

"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import {
  Send,
  Smile,
  Paperclip,
  MoreVertical,
  Hash,
  Users,
  Search,
  Settings,
  Phone,
  Video,
  UserPlus,
  MessageSquare,
  Heart,
  ThumbsUp,
  <PERSON>gh,
  Angry,
  Frown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  ChatMessage, 
  ChatChannel, 
  User 
} from '@/types/social'

interface ChatSystemProps {
  currentUser: User
  channels: ChatChannel[]
  activeChannelId: string
  messages: ChatMessage[]
  onSendMessage: (content: string, channelId: string) => void
  onJoinChannel: (channelId: string) => void
  onLeaveChannel: (channelId: string) => void
  onReactToMessage: (messageId: string, emoji: string) => void
  onStartCall: (channelId: string, type: 'voice' | 'video') => void
  className?: string
}

const REACTION_EMOJIS = [
  { emoji: '👍', icon: ThumbsUp, label: 'Like' },
  { emoji: '❤️', icon: Heart, label: 'Love' },
  { emoji: '😂', icon: Laugh, label: 'Laugh' },
  { emoji: '😮', icon: MessageSquare, label: 'Wow' },
  { emoji: '😢', icon: Frown, label: 'Sad' },
  { emoji: '😠', icon: Angry, label: 'Angry' }
]

export function ChatSystem({
  currentUser,
  channels,
  activeChannelId,
  messages,
  onSendMessage,
  onJoinChannel,
  onLeaveChannel: _onLeaveChannel,
  onReactToMessage,
  onStartCall,
  className = ""
}: ChatSystemProps) {
  const [messageInput, setMessageInput] = useState('')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [_selectedMessage, setSelectedMessage] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const activeChannel = channels.find(c => c.id === activeChannelId)
  const filteredMessages = messages.filter(m => 
    m.channelId === activeChannelId &&
    (searchQuery === '' || m.content.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = () => {
    if (messageInput.trim() && activeChannelId) {
      onSendMessage(messageInput.trim(), activeChannelId)
      setMessageInput('')
      inputRef.current?.focus()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'public': return <Hash className="w-4 h-4" />
      case 'private': return <Users className="w-4 h-4" />
      case 'direct': return <MessageSquare className="w-4 h-4" />
      default: return <Hash className="w-4 h-4" />
    }
  }

  const renderMessage = (message: ChatMessage) => {
    const isOwnMessage = message.senderId === currentUser.id
    const showReactions = message.reactions.length > 0

    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`group flex gap-3 p-3 hover:bg-gray-800/50 ${
          isOwnMessage ? 'flex-row-reverse' : ''
        }`}
      >
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={message.senderAvatar} />
          <AvatarFallback>{message.senderName[0]}</AvatarFallback>
        </Avatar>

        <div className={`flex-1 space-y-1 ${isOwnMessage ? 'text-right' : ''}`}>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-white">{message.senderName}</span>
            <span className="text-xs text-gray-400">{formatTime(message.timestamp)}</span>
            {message.isEdited && (
              <span className="text-xs text-gray-500">(edited)</span>
            )}
          </div>

          <div className={`inline-block max-w-md p-3 rounded-lg ${
            isOwnMessage 
              ? 'bg-cyan-600 text-white' 
              : 'bg-gray-700 text-gray-100'
          }`}>
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          </div>

          {/* Reactions */}
          {showReactions && (
            <div className="flex gap-1 flex-wrap">
              {message.reactions.map((reaction) => (
                <Button
                  key={reaction.emoji}
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs bg-gray-800 hover:bg-gray-700"
                  onClick={() => onReactToMessage(message.id, reaction.emoji)}
                >
                  <span className="mr-1">{reaction.emoji}</span>
                  <span>{reaction.count}</span>
                </Button>
              ))}
            </div>
          )}

          {/* Quick reactions on hover */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex gap-1 mt-1">
              {REACTION_EMOJIS.slice(0, 3).map((reaction) => (
                <Button
                  key={reaction.emoji}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-gray-700"
                  onClick={() => onReactToMessage(message.id, reaction.emoji)}
                >
                  <span className="text-xs">{reaction.emoji}</span>
                </Button>
              ))}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-gray-700"
                onClick={() => setSelectedMessage(message.id)}
              >
                <MoreVertical className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className={`flex h-full bg-gray-900 ${className}`}>
      {/* Channel Sidebar */}
      <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center gap-2 mb-3">
            <MessageSquare className="w-5 h-5 text-cyan-400" />
            <h2 className="font-semibold text-white">Channels</h2>
          </div>
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search channels..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-gray-700 border-gray-600"
            />
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2 space-y-1">
            {channels.map((channel) => (
              <Button
                key={channel.id}
                variant={channel.id === activeChannelId ? "secondary" : "ghost"}
                className={`w-full justify-start gap-2 ${
                  channel.id === activeChannelId 
                    ? 'bg-cyan-600 text-white' 
                    : 'text-gray-300 hover:text-white hover:bg-gray-700'
                }`}
                onClick={() => onJoinChannel(channel.id)}
              >
                {getChannelIcon(channel.type)}
                <span className="truncate">{channel.name}</span>
                {channel.members.length > 0 && (
                  <Badge variant="outline" className="ml-auto text-xs">
                    {channel.members.length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </ScrollArea>

        {/* User Status */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center gap-3">
            <Avatar className="w-8 h-8">
              <AvatarImage src={currentUser.avatar?.imageUrl} />
              <AvatarFallback>{currentUser.displayName[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {currentUser.displayName}
              </p>
              <p className="text-xs text-gray-400 capitalize">{currentUser.status}</p>
            </div>
            <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeChannel ? (
          <>
            {/* Channel Header */}
            <div className="p-4 border-b border-gray-700 bg-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getChannelIcon(activeChannel.type)}
                  <div>
                    <h3 className="font-semibold text-white">{activeChannel.name}</h3>
                    {activeChannel.description && (
                      <p className="text-sm text-gray-400">{activeChannel.description}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onStartCall(activeChannel.id, 'voice')}
                        >
                          <Phone className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Start voice call</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onStartCall(activeChannel.id, 'video')}
                        >
                          <Video className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Start video call</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <Button variant="ghost" size="sm">
                    <UserPlus className="w-4 h-4" />
                  </Button>

                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-2">
                {filteredMessages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-700 bg-gray-800">
              <div className="flex items-end gap-3">
                <div className="flex-1 relative">
                  <Input
                    ref={inputRef}
                    placeholder={`Message ${activeChannel.name}...`}
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pr-20 bg-gray-700 border-gray-600 resize-none"
                    maxLength={2000}
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    >
                      <Smile className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                      <Paperclip className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim()}
                  className="bg-cyan-600 hover:bg-cyan-700"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Select a channel to start chatting</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChatSystem

"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Rocket, X } from 'lucide-react'

interface FloatingCTAProps {
  onSignupClick: () => void
}

export function FloatingCTA({ onSignupClick }: FloatingCTAProps) {
  const [isVisible, setIsVisible] = React.useState(true)

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 100 }}
      className="fixed bottom-6 right-6 z-40"
    >
      <motion.div
        whileHover={{ scale: 1.02 }}
        className="bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl p-4 shadow-2xl border border-cyan-400/20 backdrop-blur-xl max-w-sm"
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center"
            >
              <Rocket className="w-4 h-4 text-white" />
            </motion.div>
            <span className="text-white font-semibold">Ready to start?</span>
          </div>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsVisible(false)}
            className="text-white/70 hover:text-white"
          >
            <X className="w-4 h-4" />
          </motion.button>
        </div>
        
        <p className="text-white/90 text-sm mb-4">
          Join thousands of young learners building their tech future!
        </p>
        
        <div className="flex gap-2">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex-1"
          >
            <Button
              onClick={onSignupClick}
              className="w-full bg-white text-cyan-600 hover:bg-gray-100 font-semibold"
            >
              Start Learning
            </Button>
          </motion.div>
        </div>
        
        <div className="flex items-center justify-center gap-4 mt-3 text-xs text-white/70">
          <span>✓ Free to start</span>
          <span>✓ Safe & secure</span>
        </div>
      </motion.div>
    </motion.div>
  )
}

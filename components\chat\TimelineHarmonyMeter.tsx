"use client"

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Sparkles, 
  TrendingUp, 
  Users, 
  Heart,
  Zap,
  Star,
  Globe,
  Activity
} from 'lucide-react'
import { TimelineHarmony } from '@/types/chat'

interface TimelineHarmonyMeterProps {
  harmony: TimelineHarmony | null
  recentContributions: Array<{
    userId: string
    userName: string
    action: string
    impact: number
    timestamp: Date
  }>
  className?: string
}

const harmonyLevels = [
  { level: 1, name: 'Emerging', color: 'from-gray-500 to-gray-600', threshold: 0 },
  { level: 2, name: 'Growing', color: 'from-blue-500 to-blue-600', threshold: 100 },
  { level: 3, name: 'Flourishing', color: 'from-green-500 to-green-600', threshold: 300 },
  { level: 4, name: 'Thriving', color: 'from-yellow-500 to-yellow-600', threshold: 600 },
  { level: 5, name: 'Radian<PERSON>', color: 'from-orange-500 to-orange-600', threshold: 1000 },
  { level: 6, name: 'Luminous', color: 'from-purple-500 to-purple-600', threshold: 1500 },
  { level: 7, name: 'Transcendent', color: 'from-pink-500 to-rose-600', threshold: 2500 },
  { level: 8, name: 'Quantum', color: 'from-cyan-500 to-blue-600', threshold: 4000 },
  { level: 9, name: 'Cosmic', color: 'from-indigo-500 to-purple-600', threshold: 6000 },
  { level: 10, name: 'Infinite', color: 'from-gradient-rainbow', threshold: 10000 }
]

export function TimelineHarmonyMeter({
  harmony,
  recentContributions,
  className = ""
}: TimelineHarmonyMeterProps) {
  const [animatedLevel, setAnimatedLevel] = useState(0)
  const [showLevelUp, setShowLevelUp] = useState(false)
  const [_previousLevel, setPreviousLevel] = useState(0)

  const currentLevel = harmony ? getCurrentHarmonyLevel(harmony.totalContributions) : harmonyLevels[0]
  const nextLevel = harmonyLevels[currentLevel.level] || harmonyLevels[harmonyLevels.length - 1]
  const progressToNext = harmony ? 
    Math.min(100, ((harmony.totalContributions - currentLevel.threshold) / (nextLevel.threshold - currentLevel.threshold)) * 100) : 0

  // Animate level changes
  useEffect(() => {
    if (harmony && animatedLevel !== currentLevel.level) {
      if (animatedLevel > 0 && currentLevel.level > animatedLevel) {
        setShowLevelUp(true)
        setPreviousLevel(animatedLevel)
        setTimeout(() => setShowLevelUp(false), 3000)
      }
      setAnimatedLevel(currentLevel.level)
    }
  }, [harmony, currentLevel.level, animatedLevel])

  function getCurrentHarmonyLevel(contributions: number) {
    for (let i = harmonyLevels.length - 1; i >= 0; i--) {
      if (contributions >= harmonyLevels[i].threshold) {
        return harmonyLevels[i]
      }
    }
    return harmonyLevels[0]
  }

  const getMultiplierColor = (multiplier: number) => {
    if (multiplier >= 3) return 'text-purple-400'
    if (multiplier >= 2) return 'text-yellow-400'
    if (multiplier >= 1.5) return 'text-green-400'
    return 'text-gray-400'
  }

  const getActivityLevel = (recentActivity: number) => {
    if (recentActivity >= 50) return { label: 'Very Active', color: 'text-green-400', icon: TrendingUp }
    if (recentActivity >= 20) return { label: 'Active', color: 'text-yellow-400', icon: Activity }
    if (recentActivity >= 5) return { label: 'Moderate', color: 'text-blue-400', icon: Activity }
    return { label: 'Quiet', color: 'text-gray-400', icon: Activity }
  }

  if (!harmony) {
    return (
      <Card className={`bg-black/40 backdrop-blur-xl border-gray-800/50 ${className}`}>
        <CardContent className="p-6 text-center">
          <Globe className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">Timeline Harmony data loading...</p>
        </CardContent>
      </Card>
    )
  }

  const activityInfo = getActivityLevel(harmony.recentActivity)
  const ActivityIcon = activityInfo.icon

  return (
    <>
      {/* Level Up Animation */}
      <AnimatePresence>
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -50 }}
            className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
          >
            <div className="bg-black/90 backdrop-blur-xl border border-cyan-500/50 rounded-xl p-8 text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className={`w-20 h-20 mx-auto mb-4 bg-gradient-to-r ${currentLevel.color} rounded-full flex items-center justify-center`}
              >
                <Star className="w-10 h-10 text-white" />
              </motion.div>
              <h3 className="text-2xl font-bold text-white mb-2">Harmony Level Up!</h3>
              <p className="text-cyan-400 text-lg">
                Timeline reached <span className="font-bold">{currentLevel.name}</span> level!
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Card className={`bg-black/40 backdrop-blur-xl border-gray-800/50 ${className}`}>
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white flex items-center gap-2">
            <div className={`w-6 h-6 bg-gradient-to-r ${currentLevel.color} rounded-full flex items-center justify-center`}>
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            Timeline Harmony
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={`bg-gradient-to-r ${currentLevel.color} text-white border-0`}>
              Level {currentLevel.level}: {currentLevel.name}
            </Badge>
            <Badge className={`${activityInfo.color} bg-gray-800/50`}>
              <ActivityIcon className="w-3 h-3 mr-1" />
              {activityInfo.label}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Progress to Next Level */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300">
                Progress to {nextLevel.name}
              </span>
              <span className="text-gray-400">
                {harmony.totalContributions}/{nextLevel.threshold}
              </span>
            </div>
            <Progress 
              value={progressToNext} 
              className="h-3"
            />
            <p className="text-xs text-gray-500 text-center">
              {nextLevel.threshold - harmony.totalContributions} more contributions needed
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Users className="w-5 h-5 text-cyan-400 mx-auto mb-1" />
              <div className="text-lg font-bold text-white">
                {harmony.participants.length}
              </div>
              <div className="text-xs text-gray-400">Participants</div>
            </div>
            
            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Heart className="w-5 h-5 text-pink-400 mx-auto mb-1" />
              <div className="text-lg font-bold text-white">
                {harmony.totalContributions}
              </div>
              <div className="text-xs text-gray-400">Total Harmony</div>
            </div>
            
            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <Zap className={`w-5 h-5 mx-auto mb-1 ${getMultiplierColor(harmony.multiplier)}`} />
              <div className={`text-lg font-bold ${getMultiplierColor(harmony.multiplier)}`}>
                {harmony.multiplier.toFixed(1)}x
              </div>
              <div className="text-xs text-gray-400">Multiplier</div>
            </div>
            
            <div className="text-center p-3 bg-gray-800/30 rounded-lg">
              <TrendingUp className="w-5 h-5 text-green-400 mx-auto mb-1" />
              <div className="text-lg font-bold text-white">
                {harmony.recentActivity}
              </div>
              <div className="text-xs text-gray-400">Recent Activity</div>
            </div>
          </div>

          {/* Recent Contributions */}
          {recentContributions.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
                <Activity className="w-4 h-4 text-cyan-400" />
                Recent Contributions
              </h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {recentContributions.slice(0, 5).map((contribution, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-2 bg-gray-800/30 rounded text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                      <span className="text-gray-300">
                        {contribution.userName}
                      </span>
                      <span className="text-gray-500">
                        {contribution.action}
                      </span>
                    </div>
                    <Badge 
                      variant={contribution.impact > 0 ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {contribution.impact > 0 ? '+' : ''}{contribution.impact}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Harmony Effects */}
          <div className="p-3 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 border border-cyan-500/30 rounded-lg">
            <h4 className="text-sm font-medium text-cyan-400 mb-2">
              ✨ Current Harmony Effects
            </h4>
            <div className="text-xs text-gray-300 space-y-1">
              <div>• {harmony.multiplier.toFixed(1)}x XP bonus for all participants</div>
              <div>• Enhanced learning opportunities unlocked</div>
              {currentLevel.level >= 3 && <div>• Special quantum events available</div>}
              {currentLevel.level >= 5 && <div>• Advanced collaboration tools enabled</div>}
              {currentLevel.level >= 7 && <div>• Timeline-wide celebration bonuses active</div>}
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

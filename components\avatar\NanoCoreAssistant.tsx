"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  MessageCircle, 
  Send, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Lightbulb,
  BookOpen,
  Code,
  Zap,
  Brain,
  <PERSON>rkles
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { NanoCore } from './NanoCore'
import { 
  NanoCoreCustomization, 
  Avatar3DSettings,
  NanoCoreAIPersonality,
  DEFAULT_NANOCORE_CUSTOMIZATION,
  DEFAULT_AVATAR_SETTINGS,
  DEFAULT_NANOCORE_AI_PERSONALITY
} from '@/types/avatar'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  emotion?: 'happy' | 'excited' | 'thinking' | 'encouraging' | 'surprised'
  animation?: string
}

interface NanoCoreAssistantProps {
  customization?: NanoCoreCustomization
  settings?: Avatar3DSettings
  personality?: NanoCoreAIPersonality
  onMessage?: (message: string) => Promise<string>
  className?: string
}

export function NanoCoreAssistant({
  customization = DEFAULT_NANOCORE_CUSTOMIZATION,
  settings = DEFAULT_AVATAR_SETTINGS,
  personality = DEFAULT_NANOCORE_AI_PERSONALITY,
  onMessage,
  className = ""
}: NanoCoreAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: `Hi! I'm ${personality.name}, your NanoCore companion! I'm here to help you learn and explore. What would you like to know?`,
      timestamp: new Date(),
      emotion: 'happy',
      animation: 'greeting'
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [currentEmotion, setCurrentEmotion] = useState<string>('happy')
  const [_currentAnimation, setCurrentAnimation] = useState<string>('idle')
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])
  
  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return
    
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsTyping(true)
    setCurrentAnimation('thinking')
    setCurrentEmotion('thinking')
    
    try {
      // Simulate AI response or use provided onMessage handler
      const response = onMessage 
        ? await onMessage(inputMessage)
        : await simulateAIResponse(inputMessage)
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        emotion: getEmotionFromResponse(response),
        animation: getAnimationFromResponse(response)
      }
      
      setTimeout(() => {
        setMessages(prev => [...prev, assistantMessage])
        setIsTyping(false)
        setCurrentAnimation(assistantMessage.animation || 'explaining')
        setCurrentEmotion(assistantMessage.emotion || 'happy')
      }, 1000 + Math.random() * 2000) // Simulate thinking time
      
    } catch (error) {
      setIsTyping(false)
      setCurrentAnimation('disappointed')
      setCurrentEmotion('disappointed')
      console.error('Failed to get AI response:', error)
    }
  }
  
  const simulateAIResponse = async (message: string): Promise<string> => {
    const lowerMessage = message.toLowerCase()
    
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      return "Hello there! Great to see you! I'm excited to help you on your learning journey. What topic interests you today?"
    }
    
    if (lowerMessage.includes('code') || lowerMessage.includes('programming')) {
      return "Coding is amazing! 🚀 It's like giving instructions to computers to create incredible things. Would you like to start with a simple programming concept, or do you have a specific language in mind?"
    }
    
    if (lowerMessage.includes('science') || lowerMessage.includes('physics')) {
      return "Science is fascinating! From tiny atoms to massive galaxies, there's so much to discover. What area of science sparks your curiosity?"
    }
    
    if (lowerMessage.includes('help') || lowerMessage.includes('learn')) {
      return "I'm here to help you learn in the most engaging way possible! I can explain concepts, give examples, and even create interactive demonstrations. What would you like to explore?"
    }
    
    return "That's an interesting question! Let me think about the best way to explain this. I love helping curious minds like yours discover new things!"
  }
  
  const getEmotionFromResponse = (response: string): Message['emotion'] => {
    if (response.includes('!') || response.includes('amazing') || response.includes('great')) {
      return 'excited'
    }
    if (response.includes('?') || response.includes('think')) {
      return 'thinking'
    }
    if (response.includes('help') || response.includes('support')) {
      return 'encouraging'
    }
    return 'happy'
  }
  
  const getAnimationFromResponse = (response: string): string => {
    if (response.includes('!') || response.includes('amazing')) {
      return 'excited'
    }
    if (response.includes('explain') || response.includes('concept')) {
      return 'explaining'
    }
    if (response.includes('help') || response.includes('support')) {
      return 'encouraging'
    }
    return 'idle'
  }
  
  const quickActions = [
    { icon: <Code className="w-4 h-4" />, label: "Learn Coding", message: "I want to learn programming!" },
    { icon: <Brain className="w-4 h-4" />, label: "Science Facts", message: "Tell me something cool about science!" },
    { icon: <Lightbulb className="w-4 h-4" />, label: "Get Ideas", message: "I need some creative project ideas!" },
    { icon: <BookOpen className="w-4 h-4" />, label: "Study Help", message: "Can you help me study?" }
  ]
  
  const getEmotionColor = (emotion: string) => {
    switch (emotion) {
      case 'happy': return 'text-green-400'
      case 'excited': return 'text-yellow-400'
      case 'thinking': return 'text-blue-400'
      case 'encouraging': return 'text-purple-400'
      case 'surprised': return 'text-orange-400'
      default: return 'text-cyan-400'
    }
  }
  
  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 h-full ${className}`}>
      {/* NanoCore Avatar */}
      <div className="space-y-4">
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                {personality.name}
              </span>
              <Badge variant="outline" className={`border-current ${getEmotionColor(currentEmotion)}`}>
                {currentEmotion}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-96 lg:h-[500px]">
              <NanoCore
                customization={customization}
                settings={settings}
                aiPersonality={personality}
                interactionMode="assistant"
                className="rounded-b-lg"
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Quick Actions */}
        <Card className="bg-gray-900/95 border-gray-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setInputMessage(action.message)
                    inputRef.current?.focus()
                  }}
                  className="flex items-center gap-2 border-gray-600 hover:border-cyan-400 text-xs"
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Chat Interface */}
      <div className="flex flex-col h-full">
        <Card className="bg-gray-900/95 border-gray-700 flex-1 flex flex-col">
          <CardHeader className="pb-4">
            <CardTitle className="text-cyan-400 flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Chat with {personality.name}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto space-y-4 mb-4 max-h-96">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-cyan-600 text-white'
                        : 'bg-gray-800 text-gray-100 border border-gray-700'
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs opacity-70">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                      {message.emotion && message.type === 'assistant' && (
                        <Badge variant="outline" className={`text-xs ${getEmotionColor(message.emotion)}`}>
                          {message.emotion}
                        </Badge>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {/* Typing indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className="bg-gray-800 border border-gray-700 p-3 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
            
            {/* Input */}
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder={`Ask ${personality.name} anything...`}
                className="flex-1 bg-gray-800 border-gray-600"
                disabled={isTyping}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isTyping}
                className="bg-cyan-600 hover:bg-cyan-700"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
            
            {/* Voice controls */}
            <div className="flex justify-center gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsListening(!isListening)}
                className={`border-gray-600 ${isListening ? 'bg-red-600 text-white' : ''}`}
              >
                {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSpeaking(!isSpeaking)}
                className={`border-gray-600 ${isSpeaking ? 'bg-green-600 text-white' : ''}`}
              >
                {isSpeaking ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default NanoCoreAssistant

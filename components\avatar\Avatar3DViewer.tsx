"use client"

import React, { Suspense, useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows } from '@react-three/drei'

import * as THREE from 'three'
import { Avatar3DProps, AvatarCustomization } from '@/types/avatar'
import { Loader2, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react'
import { Button } from '@/components/ui/button'

// 3D Avatar Model Component
function AvatarModel({ 
  customization, 
  animationState: _animationState,
  onLoad,
  onError: _onError
}: {
  customization: AvatarCustomization
  animationState?: any
  onLoad?: () => void
  onError?: (error: Error) => void
}) {
  const meshRef = useRef<THREE.Group>(null)
  const [mixer, setMixer] = useState<THREE.AnimationMixer | null>(null)
  
  // For now, always use fallback avatar since we don't have GLB files
  const [useGLTF, setUseGLTF] = useState(false)

  // Only try to load GLTF if we have a valid Ready Player Me URL
  const hasValidUrl = !!(customization.readyPlayerMeUrl && customization.readyPlayerMeUrl.includes('readyplayer.me'))

  // Always call useGLTF with a fallback URL to avoid conditional hook calls
  const avatarUrl = hasValidUrl ? customization.readyPlayerMeUrl! : '/models/fallback-avatar.glb'
  const gltf = (useGLTF as any)(avatarUrl)

  useEffect(() => {
    if (hasValidUrl) {
      setUseGLTF(true)
    } else {
      setUseGLTF(false)
    }
  }, [hasValidUrl])

  useEffect(() => {
    if (gltf && gltf.animations && gltf.animations.length > 0) {
      const newMixer = new THREE.AnimationMixer(gltf.scene)
      setMixer(newMixer)

      // Play idle animation by default
      const idleClip = gltf.animations.find((clip: THREE.AnimationClip) =>
        clip.name.toLowerCase().includes('idle') ||
        clip.name.toLowerCase().includes(customization.idleAnimation)
      )

      if (idleClip) {
        const action = newMixer.clipAction(idleClip)
        action.play()
      }
    }

    // Always call onLoad to indicate the avatar is ready (even if it's fallback)
    onLoad?.()

    return () => {
      if (mixer) {
        mixer.stopAllAction()
      }
    }
  }, [gltf, customization.idleAnimation, onLoad, mixer])

  useFrame((state, delta) => {
    if (mixer) {
      mixer.update(delta)
    }
    
    // Subtle floating animation
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  // Fallback geometric avatar
  const FallbackAvatar = () => (
    <group ref={meshRef}>
      {/* Head */}
      <mesh position={[0, 1.6, 0]}>
        <sphereGeometry args={[0.15, 32, 32]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      {/* Body */}
      <mesh position={[0, 1, 0]}>
        <cylinderGeometry args={[0.2, 0.25, 0.8, 8]} />
        <meshStandardMaterial color={customization.outfit === 'cyberpunk' ? '#00ffff' : '#4a5568'} />
      </mesh>
      
      {/* Arms */}
      <mesh position={[-0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 8]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      <mesh position={[0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 8]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      {/* Legs */}
      <mesh position={[-0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      <mesh position={[0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      
      {/* Hair */}
      <mesh position={[0, 1.75, 0]}>
        <sphereGeometry args={[0.18, 16, 16]} />
        <meshStandardMaterial color={customization.hairColor} />
      </mesh>
    </group>
  )

  // Always use fallback avatar for now, unless we have a valid Ready Player Me model
  if (gltf && gltf.scene && useGLTF) {
    return (
      <primitive
        ref={meshRef}
        object={gltf.scene}
        scale={[customization.height, customization.height, customization.height]}
      />
    )
  }

  // Use fallback avatar by default
  return <FallbackAvatar />
}

// Loading component
function AvatarLoader() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-cyan-400" />
        <p className="text-sm text-gray-400">Loading avatar...</p>
      </div>
    </div>
  )
}

// Main Avatar 3D Viewer Component
export function Avatar3DViewer({
  customization,
  settings,
  animationState,
  onLoad,
  onError,
  className = "",
  style
}: Avatar3DProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [cameraDistance, setCameraDistance] = useState(settings.cameraDistance)
  const [autoRotate, setAutoRotate] = useState(settings.autoRotate)

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  // Set loaded immediately since we're using fallback avatar
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 100) // Small delay to show loading briefly

    return () => clearTimeout(timer)
  }, [])

  const _resetCamera = () => {
    setCameraDistance(settings.cameraDistance)
    setAutoRotate(false)
  }

  const zoomIn = () => {
    setCameraDistance(prev => Math.max(prev - 0.5, 1))
  }

  const zoomOut = () => {
    setCameraDistance(prev => Math.min(prev + 0.5, 10))
  }

  return (
    <div className={`relative w-full h-full bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden ${className}`} style={style}>
      {/* 3D Canvas */}
      <Canvas
        shadows
        camera={{ position: [0, 0, cameraDistance], fov: 50 }}
        gl={{ antialias: true, alpha: true }}
      >
        <Suspense fallback={null}>
          {/* Lighting */}
          <ambientLight intensity={settings.ambientLightIntensity} />
          <directionalLight 
            position={[5, 5, 5]} 
            intensity={settings.directionalLightIntensity}
            castShadow
            shadow-mapSize-width={1024}
            shadow-mapSize-height={1024}
          />
          <pointLight position={[-5, 5, 5]} intensity={0.3} color="#00ffff" />
          
          {/* Environment */}
          <Environment preset="city" />
          
          {/* Avatar Model */}
          <AvatarModel
            customization={customization}
            animationState={animationState}
            onLoad={handleLoad}
            onError={onError}
          />
          
          {/* Ground shadow */}
          <ContactShadows 
            position={[0, 0, 0]} 
            opacity={0.4} 
            scale={3} 
            blur={2} 
            far={2} 
          />
          
          {/* Camera controls */}
          <OrbitControls
            enablePan={false}
            enableZoom={true}
            enableRotate={true}
            autoRotate={autoRotate}
            autoRotateSpeed={2}
            minDistance={1}
            maxDistance={10}
            minPolarAngle={Math.PI / 6}
            maxPolarAngle={Math.PI - Math.PI / 6}
          />
        </Suspense>
      </Canvas>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <AvatarLoader />
        </div>
      )}

      {/* Controls overlay */}
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        <Button
          size="sm"
          variant="outline"
          className="bg-black/50 border-gray-600 hover:bg-gray-800"
          onClick={() => setAutoRotate(!autoRotate)}
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          className="bg-black/50 border-gray-600 hover:bg-gray-800"
          onClick={zoomIn}
        >
          <ZoomIn className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          className="bg-black/50 border-gray-600 hover:bg-gray-800"
          onClick={zoomOut}
        >
          <ZoomOut className="w-4 h-4" />
        </Button>
      </div>

      {/* Avatar info overlay */}
      <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg p-3">
        <p className="text-sm text-white font-medium">3D Avatar</p>
        <p className="text-xs text-gray-300">
          Quality: {settings.quality} | Animations: {settings.enableAnimations ? 'On' : 'Off'}
        </p>
      </div>
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { 
  MessageSquare, 
  Users, 
  BookOpen, 
  Folder, 
  Calendar,
  Bell,
  Search,
  Settings,
  TrendingUp,
  Zap
} from 'lucide-react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ChatSystem } from './ChatSystem'
import { MentorshipHub } from './MentorshipHub'
import { StudyGroups } from './StudyGroups'
import { CollaborationSpaces } from './CollaborationSpaces'
import { SocialEvents } from './SocialEvents'
import { 
  User as UserType,
  ChatChannel,
  ChatMessage,
  MentorProfile,
  MentorshipRequest,
  MentorshipSession,
  StudyGroup,
  CollaborationSpace,
  SocialEvent,
  Notification,
  UserConnection
} from '@/types/social'

interface SocialHubProps {
  currentUser: UserType
  // Chat data
  channels: ChatChannel[]
  messages: ChatMessage[]
  // Mentorship data
  mentors: MentorProfile[]
  mentorshipRequests: MentorshipRequest[]
  mentorshipSessions: MentorshipSession[]
  // Study groups data
  studyGroups: StudyGroup[]
  // Collaboration data
  collaborationSpaces: CollaborationSpace[]
  // Events data
  socialEvents: SocialEvent[]
  // Social data
  notifications: Notification[]
  connections: UserConnection[]
  // Handlers
  onSendMessage: (content: string, channelId: string) => void
  onJoinChannel: (channelId: string) => void
  onRequestMentorship: (mentorId: string, request: Partial<MentorshipRequest>) => void
  onJoinStudyGroup: (groupId: string) => void
  onJoinCollaborationSpace: (spaceId: string) => void
  onJoinEvent: (eventId: string) => void
  className?: string
}

export function SocialHub({
  currentUser,
  channels,
  messages,
  mentors,
  mentorshipRequests,
  mentorshipSessions,
  studyGroups,
  collaborationSpaces,
  socialEvents,
  notifications,
  connections,
  onSendMessage,
  onJoinChannel,
  onRequestMentorship,
  onJoinStudyGroup,
  onJoinCollaborationSpace,
  onJoinEvent,
  className = ""
}: SocialHubProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [activeChatChannel, setActiveChatChannel] = useState(channels[0]?.id || '')

  // Calculate social metrics
  const unreadNotifications = notifications.filter(n => !n.isRead).length
  const activeConnections = connections.filter(c => c.status === 'accepted').length
  const upcomingEvents = socialEvents.filter(e => e.startTime > new Date()).length
  const myStudyGroups = studyGroups.filter(g => g.currentMembers.includes(currentUser.id)).length
  const mySpaces = collaborationSpaces.filter(s => s.members.includes(currentUser.id)).length

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-cyan-900/50 to-purple-900/50 border-cyan-600">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={currentUser.avatar?.imageUrl} />
              <AvatarFallback>{currentUser.displayName[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-white">Welcome back, {currentUser.displayName}!</h2>
              <p className="text-cyan-200 mt-1">Ready to connect and learn with your community?</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-cyan-400">Level {currentUser.level}</div>
              <div className="text-sm text-cyan-200">{currentUser.badges.length} badges earned</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-600 rounded-lg">
                <Users className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-xl font-bold text-white">{activeConnections}</p>
                <p className="text-sm text-gray-400">Connections</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-600 rounded-lg">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-xl font-bold text-white">{myStudyGroups}</p>
                <p className="text-sm text-gray-400">Study Groups</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-600 rounded-lg">
                <Folder className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-xl font-bold text-white">{mySpaces}</p>
                <p className="text-sm text-gray-400">Collaborations</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-600 rounded-lg">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-xl font-bold text-white">{upcomingEvents}</p>
                <p className="text-sm text-gray-400">Upcoming Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Recent Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {notifications.slice(0, 5).map((notification) => (
                <div key={notification.id} className="flex items-start gap-3 p-3 bg-gray-700 rounded-lg">
                  <div className={`w-2 h-2 rounded-full mt-2 ${notification.isRead ? 'bg-gray-500' : 'bg-cyan-400'}`} />
                  <div className="flex-1">
                    <p className="text-sm text-white">{notification.title}</p>
                    <p className="text-xs text-gray-400">{notification.content}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
                        Math.floor((notification.createdAt.getTime() - Date.now()) / (1000 * 60 * 60)),
                        'hour'
                      )}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button 
                className="w-full justify-start bg-cyan-600 hover:bg-cyan-700"
                onClick={() => setActiveTab('chat')}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Join a conversation
              </Button>
              <Button 
                className="w-full justify-start bg-green-600 hover:bg-green-700"
                onClick={() => setActiveTab('mentorship')}
              >
                <Users className="w-4 h-4 mr-2" />
                Find a mentor
              </Button>
              <Button 
                className="w-full justify-start bg-purple-600 hover:bg-purple-700"
                onClick={() => setActiveTab('study-groups')}
              >
                <BookOpen className="w-4 h-4 mr-2" />
                Join study group
              </Button>
              <Button 
                className="w-full justify-start bg-yellow-600 hover:bg-yellow-700"
                onClick={() => setActiveTab('events')}
              >
                <Calendar className="w-4 h-4 mr-2" />
                Browse events
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Featured Content */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Trending This Week
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="font-medium text-white mb-2">🔥 Hot Study Group</h4>
              <p className="text-sm text-gray-300">Advanced React Patterns</p>
              <p className="text-xs text-gray-400 mt-1">42 members • Very active</p>
            </div>
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="font-medium text-white mb-2">⭐ Featured Mentor</h4>
              <p className="text-sm text-gray-300">Sarah Chen - ML Engineer</p>
              <p className="text-xs text-gray-400 mt-1">4.9 rating • 50+ sessions</p>
            </div>
            <div className="p-4 bg-gray-700 rounded-lg">
              <h4 className="font-medium text-white mb-2">🎯 Upcoming Event</h4>
              <p className="text-sm text-gray-300">AI Workshop Series</p>
              <p className="text-xs text-gray-400 mt-1">Tomorrow • 120 attending</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className={`min-h-screen bg-gray-900 ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-700 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-white">Social Hub</h1>
              {unreadNotifications > 0 && (
                <Badge className="bg-red-600 text-white">
                  {unreadNotifications} new
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search people, groups, events..."
                  className="pl-10 w-64 bg-gray-700 border-gray-600"
                />
              </div>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <Avatar className="w-8 h-8">
                <AvatarImage src={currentUser.avatar?.imageUrl} />
                <AvatarFallback>{currentUser.displayName[0]}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 bg-gray-800 mb-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="mentorship">Mentorship</TabsTrigger>
            <TabsTrigger value="study-groups">Study Groups</TabsTrigger>
            <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderOverview()}
          </TabsContent>

          <TabsContent value="chat">
            <ChatSystem
              currentUser={currentUser}
              channels={channels}
              activeChannelId={activeChatChannel}
              messages={messages}
              onSendMessage={onSendMessage}
              onJoinChannel={(channelId) => {
                setActiveChatChannel(channelId)
                onJoinChannel(channelId)
              }}
              onLeaveChannel={() => {}}
              onReactToMessage={() => {}}
              onStartCall={() => {}}
            />
          </TabsContent>

          <TabsContent value="mentorship">
            <MentorshipHub
              currentUser={currentUser}
              mentors={mentors}
              requests={mentorshipRequests}
              sessions={mentorshipSessions}
              onRequestMentorship={onRequestMentorship}
              onAcceptRequest={() => {}}
              onScheduleSession={() => {}}
              onJoinSession={() => {}}
            />
          </TabsContent>

          <TabsContent value="study-groups">
            <StudyGroups
              currentUser={currentUser}
              groups={studyGroups}
              onJoinGroup={onJoinStudyGroup}
              onLeaveGroup={() => {}}
              onCreateGroup={() => {}}
              onStartMeeting={() => {}}
              onSubmitActivity={() => {}}
            />
          </TabsContent>

          <TabsContent value="collaboration">
            <CollaborationSpaces
              currentUser={currentUser}
              spaces={collaborationSpaces}
              onCreateSpace={() => {}}
              onJoinSpace={onJoinCollaborationSpace}
              onLeaveSpace={() => {}}
              onUpdateTask={() => {}}
              onCreateDocument={() => {}}
              onOpenDocument={() => {}}
            />
          </TabsContent>

          <TabsContent value="events">
            <SocialEvents
              currentUser={currentUser}
              events={socialEvents}
              onCreateEvent={() => {}}
              onJoinEvent={onJoinEvent}
              onLeaveEvent={() => {}}
              onJoinWaitlist={() => {}}
              onStartEvent={() => {}}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default SocialHub

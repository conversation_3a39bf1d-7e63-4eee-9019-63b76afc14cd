"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Folder, 
  Users, 
  Calendar, 
  Clock, 
  FileText,
  Code,
  Presentation,
  MessageSquare,
  Plus,
  Search,
  Share,
  Lock,
  CheckCircle,
  Circle,
  AlertCircle,
  Target,
  Edit,
  Download
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  CollaborationSpace, 
  CollaborationTask, 
  SharedDocument,
  User as UserType 
} from '@/types/social'

interface CollaborationSpacesProps {
  currentUser: UserType
  spaces: CollaborationSpace[]
  onCreateSpace: (spaceData: Partial<CollaborationSpace>) => void
  onJoinSpace: (spaceId: string) => void
  onLeaveSpace: (spaceId: string) => void
  onUpdateTask: (taskId: string, updates: Partial<CollaborationTask>) => void
  onCreateDocument: (spaceId: string, docData: Partial<SharedDocument>) => void
  onOpenDocument: (documentId: string) => void
  className?: string
}

export function CollaborationSpaces({
  currentUser,
  spaces,
  onCreateSpace,
  onJoinSpace,
  onLeaveSpace,
  onUpdateTask,
  onCreateDocument: _onCreateDocument,
  onOpenDocument,
  className = ""
}: CollaborationSpacesProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const [selectedSpace, setSelectedSpace] = useState<CollaborationSpace | null>(null)

  const mySpaces = spaces.filter(space => space.members.includes(currentUser.id))
  const publicSpaces = spaces.filter(space => 
    space.isPublic && 
    !space.members.includes(currentUser.id)
  )

  const filteredSpaces = publicSpaces.filter(space => 
    (searchQuery === '' || 
     space.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     space.description.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (selectedType === null || space.type === selectedType)
  )

  const spaceTypes = ['project', 'study', 'creative', 'research']

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'done': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'in_progress': return <Circle className="w-4 h-4 text-blue-400" />
      case 'review': return <AlertCircle className="w-4 h-4 text-yellow-400" />
      default: return <Circle className="w-4 h-4 text-gray-400" />
    }
  }

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'code': return <Code className="w-4 h-4" />
      case 'presentation': return <Presentation className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  const calculateSpaceProgress = (space: CollaborationSpace) => {
    const completedTasks = space.tasks.filter(task => task.status === 'done').length
    const totalTasks = space.tasks.length
    return totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
  }

  const renderSpaceCard = (space: CollaborationSpace, isMember: boolean = false) => {
    const progress = calculateSpaceProgress(space)
    const activeTasks = space.tasks.filter(task => task.status === 'in_progress').length
    const upcomingMilestones = space.milestones.filter(m => !m.isCompleted && m.dueDate > new Date()).length

    return (
      <motion.div
        key={space.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="group cursor-pointer"
        onClick={() => setSelectedSpace(space)}
      >
        <Card className="bg-gray-800 border-gray-700 hover:border-cyan-400 transition-all">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Folder className="w-5 h-5 text-cyan-400" />
                    <h3 className="font-semibold text-white text-lg">{space.name}</h3>
                    {!space.isPublic && <Lock className="w-4 h-4 text-gray-400" />}
                  </div>
                  <p className="text-sm text-gray-300 line-clamp-2">{space.description}</p>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs border-cyan-400 text-cyan-400 capitalize">
                      {space.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs capitalize">
                      {space.status}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-gray-400">
                    <Users className="w-4 h-4" />
                    <span>{space.members.length}</span>
                  </div>
                </div>
              </div>

              {/* Progress */}
              {isMember && space.tasks.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Progress</span>
                    <span className="text-white">{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}

              {/* Stats */}
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <CheckCircle className="w-4 h-4" />
                  <span>{activeTasks} active tasks</span>
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="w-4 h-4" />
                  <span>{space.documents.length} documents</span>
                </div>
                <div className="flex items-center gap-1">
                  <Target className="w-4 h-4" />
                  <span>{upcomingMilestones} milestones</span>
                </div>
              </div>

              {/* Team */}
              <div className="flex items-center justify-between">
                <div className="flex -space-x-2">
                  {space.members.slice(0, 4).map((memberId, index) => (
                    <Avatar key={memberId} className="w-8 h-8 border-2 border-gray-800">
                      <AvatarImage src={`/api/avatar/${memberId}`} />
                      <AvatarFallback className="text-xs">U{index + 1}</AvatarFallback>
                    </Avatar>
                  ))}
                  {space.members.length > 4 && (
                    <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-gray-800 flex items-center justify-center">
                      <span className="text-xs text-gray-300">+{space.members.length - 4}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  {isMember ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation()
                        onLeaveSpace(space.id)
                      }}
                    >
                      Leave
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        onJoinSpace(space.id)
                      }}
                      className="bg-cyan-600 hover:bg-cyan-700"
                    >
                      Join
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  const renderTaskCard = (task: CollaborationTask) => (
    <Card key={task.id} className="bg-gray-800 border-gray-700">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            <div className="flex items-center gap-2">
              {getTaskStatusIcon(task.status)}
              <h4 className="font-medium text-white">{task.title}</h4>
              <Badge variant="outline" className="text-xs capitalize">
                {task.priority}
              </Badge>
            </div>
            {task.description && (
              <p className="text-sm text-gray-300">{task.description}</p>
            )}
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>{task.assignedTo.length} assigned</span>
              </div>
              {task.dueDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Due {task.dueDate.toLocaleDateString()}</span>
                </div>
              )}
              {task.timeEstimate && (
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{task.timeEstimate}h estimated</span>
                </div>
              )}
            </div>
            {task.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {task.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onUpdateTask(task.id, { status: 'in_progress' })}
            >
              <Edit className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderDocumentCard = (document: SharedDocument) => (
    <Card key={document.id} className="bg-gray-800 border-gray-700 cursor-pointer hover:border-cyan-400 transition-colors">
      <CardContent className="p-4" onClick={() => onOpenDocument(document.id)}>
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            <div className="flex items-center gap-2">
              {getDocumentIcon(document.type)}
              <h4 className="font-medium text-white">{document.name}</h4>
              {document.isLocked && <Lock className="w-4 h-4 text-yellow-400" />}
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <span>v{document.version}</span>
              <span>Last edited {document.lastEditedAt.toLocaleDateString()}</span>
              <span>{document.collaborators.length} collaborators</span>
            </div>
            {document.comments.length > 0 && (
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <MessageSquare className="w-4 h-4" />
                <span>{document.comments.length} comments</span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="ghost">
              <Download className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost">
              <Share className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Collaboration Spaces</h1>
          <p className="text-gray-400 mt-1">Work together on projects and share knowledge</p>
        </div>
        <Button
          onClick={() => onCreateSpace({})}
          className="bg-cyan-600 hover:bg-cyan-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Space
        </Button>
      </div>

      <Tabs defaultValue="my-spaces" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-gray-800">
          <TabsTrigger value="my-spaces">My Spaces ({mySpaces.length})</TabsTrigger>
          <TabsTrigger value="discover">Discover</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="my-spaces" className="space-y-6">
          {mySpaces.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {mySpaces.map(space => renderSpaceCard(space, true))}
            </div>
          ) : (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Folder className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No collaboration spaces yet</h3>
                <p className="text-gray-400 mb-4">Create or join a space to start collaborating</p>
                <Button
                  onClick={() => onCreateSpace({})}
                  className="bg-cyan-600 hover:bg-cyan-700"
                >
                  Create Your First Space
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="discover" className="space-y-6">
          {/* Search and Filters */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search collaboration spaces..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={selectedType ? "outline" : "default"}
                    size="sm"
                    onClick={() => setSelectedType(null)}
                  >
                    All Types
                  </Button>
                  {spaceTypes.map((type) => (
                    <Button
                      key={type}
                      variant={selectedType === type ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedType(selectedType === type ? null : type)}
                      className="capitalize"
                    >
                      {type}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Spaces Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredSpaces.map(space => renderSpaceCard(space, false))}
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">My Tasks</h3>
            <Button size="sm" className="bg-cyan-600 hover:bg-cyan-700">
              <Plus className="w-4 h-4 mr-1" />
              New Task
            </Button>
          </div>
          {mySpaces.flatMap(space => space.tasks)
            .filter(task => task.assignedTo.includes(currentUser.id))
            .slice(0, 10)
            .map(renderTaskCard)}
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">Recent Documents</h3>
            <Button size="sm" className="bg-cyan-600 hover:bg-cyan-700">
              <Plus className="w-4 h-4 mr-1" />
              New Document
            </Button>
          </div>
          {mySpaces.flatMap(space => space.documents)
            .sort((a, b) => b.lastEditedAt.getTime() - a.lastEditedAt.getTime())
            .slice(0, 10)
            .map(renderDocumentCard)}
        </TabsContent>
      </Tabs>

      {/* Space Detail Modal */}
      <AnimatePresence>
        {selectedSpace && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedSpace(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-lg border border-gray-700 max-w-4xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-cyan-400">{selectedSpace.name}</h2>
                  <Button variant="outline" onClick={() => setSelectedSpace(null)}>
                    Close
                  </Button>
                </div>
                
                <p className="text-gray-300">{selectedSpace.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Recent Tasks</h3>
                    {selectedSpace.tasks.slice(0, 3).map(renderTaskCard)}
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">Documents</h3>
                    {selectedSpace.documents.slice(0, 3).map(renderDocumentCard)}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default CollaborationSpaces

"use client"

import React, { useEffect, useRef } from 'react'
import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import { NanoCoreCustomization, DEFAULT_NANOCORE_CUSTOMIZATION } from '@/types/avatar'

interface NanoCoreVanillaProps {
  customization?: NanoCoreCustomization
  className?: string
  style?: React.CSSProperties
}

export function NanoCoreVanilla({
  customization = DEFAULT_NANOCORE_CUSTOMIZATION,
  className = "",
  style
}: NanoCoreVanillaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const sceneRef = useRef<{
    scene: THREE.Scene
    camera: THREE.PerspectiveCamera
    renderer: THREE.WebGLRenderer
    avatar: THREE.Group
    controls: OrbitControls
    animationId: number
  } | null>(null)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Initialize Three.js scene
    const scene = new THREE.Scene()
    scene.background = new THREE.Color('#0d0d0d')

    const camera = new THREE.PerspectiveCamera(
      45,
      container.clientWidth / container.clientHeight,
      0.1,
      1000
    )
    camera.position.set(0, 1.5, 5)

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setSize(container.clientWidth, container.clientHeight)
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    container.appendChild(renderer.domElement)

    const controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05

    // Lighting setup
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(5, 5, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 1024
    directionalLight.shadow.mapSize.height = 1024
    scene.add(directionalLight)

    const pointLight = new THREE.PointLight(0x00ffff, 0.3)
    pointLight.position.set(-5, 5, 5)
    scene.add(pointLight)

    // Create avatar based on customization
    const avatar = createNanoCoreAvatar(customization)
    scene.add(avatar)

    // Ground plane
    const planeGeom = new THREE.PlaneGeometry(10, 10)
    const planeMat = new THREE.MeshStandardMaterial({ 
      color: 0x111111,
      transparent: true,
      opacity: 0.3
    })
    const plane = new THREE.Mesh(planeGeom, planeMat)
    plane.rotation.x = -Math.PI / 2
    plane.receiveShadow = true
    scene.add(plane)

    // Animation loop
    let animationId: number = 0
    const animate = () => {
      animationId = requestAnimationFrame(animate)
      
      // Gentle rotation
      avatar.rotation.y += 0.005
      
      // Floating animation
      avatar.position.y = Math.sin(Date.now() * 0.001) * 0.1
      
      controls.update()
      renderer.render(scene, camera)
    }
    animate()

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return
      
      camera.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight
      camera.updateProjectionMatrix()
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight)
    }
    window.addEventListener('resize', handleResize)

    // Store references
    sceneRef.current = {
      scene,
      camera,
      renderer,
      avatar,
      controls,
      animationId
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize)
      cancelAnimationFrame(animationId)
      if (container && renderer.domElement) {
        container.removeChild(renderer.domElement)
      }
      renderer.dispose()
    }
  }, [customization])

  // Update avatar when customization changes
  useEffect(() => {
    if (!sceneRef.current) return

    const { scene, avatar } = sceneRef.current
    
    // Remove old avatar
    scene.remove(avatar)
    
    // Create new avatar with updated customization
    const newAvatar = createNanoCoreAvatar(customization)
    scene.add(newAvatar)
    
    // Update reference
    sceneRef.current.avatar = newAvatar
  }, [customization])

  return (
    <div 
      ref={containerRef} 
      className={`w-full h-full ${className}`}
      style={style}
    />
  )
}

// Enhanced avatar creation function based on your original code
function createNanoCoreAvatar(customization: NanoCoreCustomization): THREE.Group {
  const avatar = new THREE.Group()
  
  // Core Head Unit - Enhanced with customization
  const headGeometry = getHeadGeometry(customization.headUnit.type, customization.headUnit.size)
  const headMaterial = new THREE.MeshStandardMaterial({
    color: new THREE.Color(customization.coreColor),
    emissive: new THREE.Color(customization.coreColor).multiplyScalar(customization.coreGlow * 0.3),
    metalness: customization.headUnit.material === 'metal' ? 0.8 : 0.2,
    roughness: customization.headUnit.material === 'crystal' ? 0.1 : 0.4,
  })
  const head = new THREE.Mesh(headGeometry, headMaterial)
  head.position.y = 1.6
  head.castShadow = true
  avatar.add(head)

  // Visual Sensors (Eyes)
  if (customization.visualSensors.type === 'dual') {
    const eyeGeom = new THREE.SphereGeometry(0.08, 12, 12)
    const eyeMat = new THREE.MeshStandardMaterial({
      color: new THREE.Color(customization.visualSensors.color),
      emissive: customization.visualSensors.glow 
        ? new THREE.Color(customization.visualSensors.color).multiplyScalar(0.5)
        : new THREE.Color(0x000000)
    })
    
    const leftEye = new THREE.Mesh(eyeGeom, eyeMat)
    leftEye.position.set(-0.15, 1.7, 0.4)
    avatar.add(leftEye)
    
    const rightEye = new THREE.Mesh(eyeGeom, eyeMat)
    rightEye.position.set(0.15, 1.7, 0.4)
    avatar.add(rightEye)
  } else if (customization.visualSensors.type === 'mono') {
    const eyeGeom = new THREE.SphereGeometry(0.12, 12, 12)
    const eyeMat = new THREE.MeshStandardMaterial({
      color: new THREE.Color(customization.visualSensors.color),
      emissive: customization.visualSensors.glow 
        ? new THREE.Color(customization.visualSensors.color).multiplyScalar(0.5)
        : new THREE.Color(0x000000)
    })
    
    const eye = new THREE.Mesh(eyeGeom, eyeMat)
    eye.position.set(0, 1.7, 0.4)
    avatar.add(eye)
  }

  // Torso Frame - Enhanced with patterns
  const torsoGeom = new THREE.CylinderGeometry(0.4, 0.4, 1.0, 16)
  const torsoMat = new THREE.MeshStandardMaterial({
    color: new THREE.Color(customization.torsoFrame.color),
    emissive: new THREE.Color(customization.coreColor).multiplyScalar(0.1),
    metalness: 0.7,
    roughness: 0.3,
    transparent: customization.torsoFrame.transparency > 0,
    opacity: 1 - customization.torsoFrame.transparency
  })
  const torso = new THREE.Mesh(torsoGeom, torsoMat)
  torso.position.y = 0.9
  torso.castShadow = true
  avatar.add(torso)

  // Enhanced Energy Core
  const orbGeom = new THREE.SphereGeometry(0.15, 16, 16)
  const orbMat = new THREE.MeshStandardMaterial({
    color: new THREE.Color(customization.energyCore.color),
    emissive: new THREE.Color(customization.energyCore.color).multiplyScalar(customization.energyCore.intensity),
    metalness: 0.8,
    roughness: 0.1,
    transparent: true,
    opacity: 0.9
  })
  const orb = new THREE.Mesh(orbGeom, orbMat)
  orb.position.set(0, 1.1, 0.4)
  avatar.add(orb)

  // Limbs with material variations
  const limbGeom = new THREE.CylinderGeometry(0.1, 0.1, 0.6, 12)
  const limbMat = new THREE.MeshStandardMaterial({ 
    color: 0x444444,
    metalness: customization.limbs.material === 'metal' ? 0.8 : 0.2,
    roughness: 0.4
  })

  // Arms with subtle animation positioning
  const leftArm = new THREE.Mesh(limbGeom, limbMat)
  leftArm.position.set(-0.6, 1.1, 0)
  leftArm.rotation.z = Math.PI / 4
  leftArm.castShadow = true
  avatar.add(leftArm)

  const rightArm = new THREE.Mesh(limbGeom, limbMat)
  rightArm.position.set(0.6, 1.1, 0)
  rightArm.rotation.z = -Math.PI / 4
  rightArm.castShadow = true
  avatar.add(rightArm)

  // Legs
  const leftLeg = new THREE.Mesh(limbGeom, limbMat)
  leftLeg.position.set(-0.25, 0.3, 0)
  leftLeg.castShadow = true
  avatar.add(leftLeg)

  const rightLeg = new THREE.Mesh(limbGeom, limbMat)
  rightLeg.position.set(0.25, 0.3, 0)
  rightLeg.castShadow = true
  avatar.add(rightLeg)

  // Evolution-based accessories
  if (customization.evolutionStage.stage === 'mentor' || customization.evolutionStage.stage === 'hero') {
    // Wisdom ring/halo
    const haloGeom = new THREE.TorusGeometry(0.6, 0.03, 8, 32)
    const haloMat = new THREE.MeshStandardMaterial({
      color: new THREE.Color(customization.energyCore.color),
      emissive: new THREE.Color(customization.energyCore.color).multiplyScalar(0.6),
      transparent: true,
      opacity: 0.7
    })
    const halo = new THREE.Mesh(haloGeom, haloMat)
    halo.position.set(0, 2.2, 0)
    halo.rotation.x = Math.PI / 2
    avatar.add(halo)
  }

  if (customization.evolutionStage.stage === 'hero') {
    // Energy wings
    const wingGeom = new THREE.PlaneGeometry(0.3, 1.0)
    const wingMat = new THREE.MeshStandardMaterial({
      color: new THREE.Color(customization.energyCore.color),
      emissive: new THREE.Color(customization.energyCore.color).multiplyScalar(0.4),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })
    
    const leftWing = new THREE.Mesh(wingGeom, wingMat)
    leftWing.position.set(-0.5, 1.0, -0.3)
    leftWing.rotation.y = 0.3
    avatar.add(leftWing)
    
    const rightWing = new THREE.Mesh(wingGeom, wingMat)
    rightWing.position.set(0.5, 1.0, -0.3)
    rightWing.rotation.y = -0.3
    avatar.add(rightWing)
  }

  // Particle effects for advanced stages
  if (customization.evolutionStage.visualUpgrades.particleEffects) {
    for (let i = 0; i < 6; i++) {
      const particleGeom = new THREE.SphereGeometry(0.02, 8, 8)
      const particleMat = new THREE.MeshStandardMaterial({
        color: new THREE.Color(customization.energyCore.color),
        emissive: new THREE.Color(customization.energyCore.color).multiplyScalar(0.8),
        transparent: true,
        opacity: 0.6
      })
      const particle = new THREE.Mesh(particleGeom, particleMat)
      
      // Position particles in orbit around avatar
      const angle = (i / 6) * Math.PI * 2
      particle.position.set(
        Math.cos(angle) * 1.2,
        1.0 + Math.sin(angle * 2) * 0.3,
        Math.sin(angle) * 1.2
      )
      avatar.add(particle)
    }
  }

  return avatar
}

// Helper function to get head geometry based on type
function getHeadGeometry(type: string, size: number): THREE.BufferGeometry {
  switch (type) {
    case 'crystal':
      return new THREE.OctahedronGeometry(0.4 * size, 0)
    case 'geometric':
      return new THREE.DodecahedronGeometry(0.35 * size, 0)
    case 'neural':
      return new THREE.IcosahedronGeometry(0.4 * size, 1)
    default:
      return new THREE.SphereGeometry(0.5 * size, 32, 32)
  }
}

export default NanoCoreVanilla

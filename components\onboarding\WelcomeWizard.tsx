"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON><PERSON>, 
  <PERSON>,
  Brain,
  Hammer,
  Search,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Trophy,
  Target,
  BookOpen,
  Gamepad2,
  Music,
  Code,
  Paintbrush,
  Camera,
  Mic
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { NanoCoreCustomizer } from '@/components/avatar/NanoCoreCustomizer'

interface WizardStep {
  id: string
  title: string
  description: string
  component: React.ReactNode
  validation?: () => boolean
  optional?: boolean
}

interface UserProfile {
  displayName: string
  bio: string
  interests: string[]
  learningGoals: string[]
  preferredTraits: string[]
  avatar: {
    type: 'nanocore' | 'custom'
    customization?: any
  }
  preferences: {
    notifications: boolean
    publicProfile: boolean
    mentorshipOpen: boolean
    collaborationStyle: 'solo' | 'small-group' | 'large-group' | 'any'
  }
}

interface WelcomeWizardProps {
  isOpen: boolean
  onComplete: (profile: UserProfile) => void
  onSkip?: () => void
  initialData?: Partial<UserProfile>
  className?: string
}

export function WelcomeWizard({
  isOpen,
  onComplete,
  onSkip,
  initialData,
  className = ""
}: WelcomeWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [profile, setProfile] = useState<UserProfile>({
    displayName: '',
    bio: '',
    interests: [],
    learningGoals: [],
    preferredTraits: [],
    avatar: { type: 'nanocore' },
    preferences: {
      notifications: true,
      publicProfile: true,
      mentorshipOpen: true,
      collaborationStyle: 'any'
    },
    ...initialData
  })

  const interests = [
    { id: 'coding', label: 'Programming', icon: Code, color: 'from-blue-500 to-cyan-500' },
    { id: 'ai', label: 'AI/ML', icon: Brain, color: 'from-purple-500 to-pink-500' },
    { id: 'design', label: 'Design', icon: Paintbrush, color: 'from-pink-500 to-red-500' },
    { id: 'gaming', label: 'Gaming', icon: Gamepad2, color: 'from-green-500 to-blue-500' },
    { id: 'music', label: 'Music', icon: Music, color: 'from-yellow-500 to-orange-500' },
    { id: 'photography', label: 'Photography', icon: Camera, color: 'from-indigo-500 to-purple-500' },
    { id: 'writing', label: 'Writing', icon: BookOpen, color: 'from-teal-500 to-green-500' },
    { id: 'speaking', label: 'Public Speaking', icon: Mic, color: 'from-red-500 to-pink-500' }
  ]

  const learningGoals = [
    'Master a new programming language',
    'Build a portfolio project',
    'Learn AI/Machine Learning',
    'Improve design skills',
    'Start a tech blog',
    'Contribute to open source',
    'Learn data science',
    'Build a mobile app',
    'Master web development',
    'Learn cybersecurity',
    'Understand blockchain',
    'Create digital art'
  ]

  const townTraits = [
    { id: 'empathy_rich', label: 'Empathy Rich', icon: Heart, color: 'text-red-400', description: 'Focus on helping and supporting others' },
    { id: 'ancient_wisdom', label: 'Ancient Wisdom', icon: Brain, color: 'text-blue-400', description: 'Share knowledge and learn from experience' },
    { id: 'builder_generation', label: 'Builder Generation', icon: Hammer, color: 'text-yellow-400', description: 'Create and build amazing things' },
    { id: 'curious_minds', label: 'Curious Minds', icon: Search, color: 'text-green-400', description: 'Ask questions and explore new ideas' }
  ]

  const updateProfile = (updates: Partial<UserProfile>) => {
    setProfile(prev => ({ ...prev, ...updates }))
  }

  const toggleArrayItem = (array: string[], item: string) => {
    return array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item]
  }

  const steps: WizardStep[] = [
    {
      id: 'basic-info',
      title: 'Tell us about yourself',
      description: 'Let&apos;s start with the basics',
      component: (
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="displayName" className="text-white">Display Name</Label>
            <Input
              id="displayName"
              placeholder="How should others know you?"
              value={profile.displayName}
              onChange={(e) => updateProfile({ displayName: e.target.value })}
              className="bg-gray-800 border-gray-600"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-white">Bio (Optional)</Label>
            <Textarea
              id="bio"
              placeholder="Tell us a bit about yourself, your interests, or what you're learning..."
              value={profile.bio}
              onChange={(e) => updateProfile({ bio: e.target.value })}
              className="bg-gray-800 border-gray-600 min-h-[100px]"
            />
          </div>
          <div className="p-4 bg-gradient-to-r from-cyan-900/50 to-purple-900/50 rounded-lg border border-cyan-600/30">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-4 h-4 text-cyan-400" />
              <span className="text-sm font-medium text-white">Pro Tip</span>
            </div>
            <p className="text-xs text-gray-300">
              Your display name will be visible to other citizens in your town. Choose something friendly and memorable!
            </p>
          </div>
        </div>
      ),
      validation: () => profile.displayName.trim().length >= 2
    },
    {
      id: 'interests',
      title: 'What interests you?',
      description: 'Select your areas of interest',
      component: (
        <div className="space-y-6">
          <p className="text-gray-300 text-center">
            Choose the topics you&apos;re passionate about (select as many as you like):
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {interests.map((interest) => (
              <motion.button
                key={interest.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => updateProfile({ 
                  interests: toggleArrayItem(profile.interests, interest.id) 
                })}
                className={`p-4 rounded-lg border transition-all ${
                  profile.interests.includes(interest.id)
                    ? 'bg-gradient-to-r ' + interest.color + ' border-white/30 text-white'
                    : 'bg-gray-800 border-gray-600 text-gray-300 hover:border-gray-500'
                }`}
              >
                <interest.icon className="w-6 h-6 mx-auto mb-2" />
                <div className="text-sm font-medium">{interest.label}</div>
              </motion.button>
            ))}
          </div>
          <div className="text-center">
            <Badge variant="outline" className="text-cyan-400 border-cyan-400">
              {profile.interests.length} selected
            </Badge>
          </div>
        </div>
      ),
      validation: () => profile.interests.length > 0
    },
    {
      id: 'learning-goals',
      title: 'What do you want to learn?',
      description: 'Set your learning objectives',
      component: (
        <div className="space-y-6">
          <p className="text-gray-300 text-center">
            What are your learning goals? (Select up to 5):
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {learningGoals.map((goal) => (
              <motion.button
                key={goal}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  if (profile.learningGoals.includes(goal)) {
                    updateProfile({ 
                      learningGoals: profile.learningGoals.filter(g => g !== goal) 
                    })
                  } else if (profile.learningGoals.length < 5) {
                    updateProfile({ 
                      learningGoals: [...profile.learningGoals, goal] 
                    })
                  }
                }}
                disabled={!profile.learningGoals.includes(goal) && profile.learningGoals.length >= 5}
                className={`p-3 rounded-lg border text-left transition-all ${
                  profile.learningGoals.includes(goal)
                    ? 'bg-cyan-600 border-cyan-400 text-white'
                    : 'bg-gray-800 border-gray-600 text-gray-300 hover:border-gray-500 disabled:opacity-50'
                }`}
              >
                <div className="flex items-center gap-2">
                  {profile.learningGoals.includes(goal) && (
                    <CheckCircle className="w-4 h-4 text-white" />
                  )}
                  <span className="text-sm">{goal}</span>
                </div>
              </motion.button>
            ))}
          </div>
          <div className="text-center">
            <Badge variant="outline" className="text-cyan-400 border-cyan-400">
              {profile.learningGoals.length}/5 selected
            </Badge>
          </div>
        </div>
      ),
      validation: () => profile.learningGoals.length > 0
    },
    {
      id: 'town-traits',
      title: 'Choose your town traits',
      description: 'What kind of community do you want to build?',
      component: (
        <div className="space-y-6">
          <p className="text-gray-300 text-center">
            Which traits do you want to focus on developing in your town?
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {townTraits.map((trait) => (
              <motion.button
                key={trait.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => updateProfile({ 
                  preferredTraits: toggleArrayItem(profile.preferredTraits, trait.id) 
                })}
                className={`p-4 rounded-lg border text-left transition-all ${
                  profile.preferredTraits.includes(trait.id)
                    ? 'bg-gray-700 border-cyan-400 text-white'
                    : 'bg-gray-800 border-gray-600 text-gray-300 hover:border-gray-500'
                }`}
              >
                <div className="flex items-start gap-3">
                  <trait.icon className={`w-6 h-6 ${trait.color} mt-0.5`} />
                  <div>
                    <h4 className="font-medium text-white mb-1">{trait.label}</h4>
                    <p className="text-sm text-gray-400">{trait.description}</p>
                  </div>
                  {profile.preferredTraits.includes(trait.id) && (
                    <CheckCircle className="w-5 h-5 text-cyan-400 ml-auto" />
                  )}
                </div>
              </motion.button>
            ))}
          </div>
        </div>
      ),
      validation: () => profile.preferredTraits.length > 0
    },
    {
      id: 'avatar',
      title: 'Customize your avatar',
      description: 'Create your unique NanoCore avatar',
      component: (
        <div className="space-y-6">
          <p className="text-gray-300 text-center">
            Customize your NanoCore avatar that will represent you in the 3D world:
          </p>
          <div className="max-w-md mx-auto">
            <NanoCoreCustomizer
              customization={profile.avatar.customization}
              onCustomizationChange={(customization) =>
                updateProfile({
                  avatar: { type: 'nanocore', customization }
                })
              }
            />
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400">
              Your avatar will evolve as you contribute to your town!
            </p>
          </div>
        </div>
      ),
      optional: true
    },
    {
      id: 'preferences',
      title: 'Set your preferences',
      description: 'Configure your experience',
      component: (
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-white">Notifications</h4>
                <p className="text-sm text-gray-400">Get notified about town activities</p>
              </div>
              <Button
                variant={profile.preferences.notifications ? "default" : "outline"}
                size="sm"
                onClick={() => updateProfile({
                  preferences: { ...profile.preferences, notifications: !profile.preferences.notifications }
                })}
              >
                {profile.preferences.notifications ? 'On' : 'Off'}
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-white">Public Profile</h4>
                <p className="text-sm text-gray-400">Let others see your profile and interests</p>
              </div>
              <Button
                variant={profile.preferences.publicProfile ? "default" : "outline"}
                size="sm"
                onClick={() => updateProfile({
                  preferences: { ...profile.preferences, publicProfile: !profile.preferences.publicProfile }
                })}
              >
                {profile.preferences.publicProfile ? 'Public' : 'Private'}
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-white">Open to Mentorship</h4>
                <p className="text-sm text-gray-400">Available for mentoring or being mentored</p>
              </div>
              <Button
                variant={profile.preferences.mentorshipOpen ? "default" : "outline"}
                size="sm"
                onClick={() => updateProfile({
                  preferences: { ...profile.preferences, mentorshipOpen: !profile.preferences.mentorshipOpen }
                })}
              >
                {profile.preferences.mentorshipOpen ? 'Open' : 'Closed'}
              </Button>
            </div>
            
            <div className="p-3 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-2">Collaboration Style</h4>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: 'solo', label: 'Solo Work' },
                  { id: 'small-group', label: 'Small Groups' },
                  { id: 'large-group', label: 'Large Groups' },
                  { id: 'any', label: 'Any Size' }
                ].map((style) => (
                  <Button
                    key={style.id}
                    variant={profile.preferences.collaborationStyle === style.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateProfile({
                      preferences: { ...profile.preferences, collaborationStyle: style.id as any }
                    })}
                  >
                    {style.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      ),
      optional: true
    },
    {
      id: 'complete',
      title: 'Welcome to your town!',
      description: 'You&apos;re all set to start your journey',
      component: (
        <div className="text-center space-y-6">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-20 h-20 mx-auto bg-gradient-to-r from-green-500 to-cyan-500 rounded-full flex items-center justify-center"
          >
            <CheckCircle className="w-10 h-10 text-white" />
          </motion.div>
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-white">
              Welcome to NanoVerse {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}!
            </h3>
            <p className="text-gray-300">
              Your profile is complete and you&apos;re ready to start collaborating with your cohort.
            </p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="p-3 bg-gray-800 rounded-lg">
                <Trophy className="w-5 h-5 text-yellow-400 mx-auto mb-1" />
                <p className="text-white font-medium">Interests</p>
                <p className="text-gray-400">{profile.interests.length} selected</p>
              </div>
              <div className="p-3 bg-gray-800 rounded-lg">
                <Target className="w-5 h-5 text-green-400 mx-auto mb-1" />
                <p className="text-white font-medium">Goals</p>
                <p className="text-gray-400">{profile.learningGoals.length} set</p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ]

  const currentStepData = steps[currentStep]
  const progress = ((currentStep + 1) / steps.length) * 100
  const canProceed = !currentStepData.validation || currentStepData.validation() || currentStepData.optional

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete(profile)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className={`bg-gray-900 rounded-xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-hidden ${className}`}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Welcome to Epoch Towns</h1>
                <p className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</p>
              </div>
            </div>
            {onSkip && (
              <Button variant="outline" size="sm" onClick={onSkip}>
                Skip Setup
              </Button>
            )}
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-white">{currentStepData.title}</h2>
                <p className="text-gray-400">{currentStepData.description}</p>
              </div>
              
              {currentStepData.component}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Previous
            </Button>
            
            <div className="flex items-center gap-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentStep 
                      ? 'bg-cyan-400' 
                      : index < currentStep 
                        ? 'bg-green-400' 
                        : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>

            <Button
              onClick={nextStep}
              disabled={!canProceed}
              className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 flex items-center gap-2"
            >
              {currentStep === steps.length - 1 ? 'Complete Setup' : 'Next'}
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default WelcomeWizard

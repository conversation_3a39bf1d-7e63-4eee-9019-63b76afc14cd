"use client"

import React, { useRef } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'
import * as THREE from 'three'
import { AvatarCustomization } from '@/types/avatar'

interface SimpleAvatar3DProps {
  customization: AvatarCustomization
  className?: string
  autoRotate?: boolean
}

function SimpleAvatarModel({ customization }: { customization: AvatarCustomization }) {
  const meshRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (meshRef.current) {
      // Subtle floating animation
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Head */}
      <mesh position={[0, 1.6, 0]}>
        <sphereGeometry args={[0.15, 32, 32]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      {/* Body */}
      <mesh position={[0, 1, 0]}>
        <cylinderGeometry args={[0.2, 0.25, 0.8, 8]} />
        <meshStandardMaterial 
          color={customization.outfit === 'cyberpunk' ? '#00ffff' : 
                customization.outfit === 'gaming' ? '#9400D3' :
                customization.outfit === 'formal' ? '#2d3748' : '#4a5568'} 
        />
      </mesh>
      
      {/* Arms */}
      <mesh position={[-0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 8]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      <mesh position={[0.35, 1.2, 0]}>
        <cylinderGeometry args={[0.05, 0.08, 0.6, 8]} />
        <meshStandardMaterial color={customization.skinTone} />
      </mesh>
      
      {/* Legs */}
      <mesh position={[-0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      <mesh position={[0.15, 0.3, 0]}>
        <cylinderGeometry args={[0.08, 0.1, 0.6, 8]} />
        <meshStandardMaterial color="#2d3748" />
      </mesh>
      
      {/* Hair */}
      <mesh position={[0, 1.75, 0]}>
        <sphereGeometry args={[0.18, 16, 16]} />
        <meshStandardMaterial color={customization.hairColor} />
      </mesh>
      
      {/* Eyes */}
      <mesh position={[-0.05, 1.65, 0.12]}>
        <sphereGeometry args={[0.02, 8, 8]} />
        <meshStandardMaterial color={customization.eyeColor} />
      </mesh>
      <mesh position={[0.05, 1.65, 0.12]}>
        <sphereGeometry args={[0.02, 8, 8]} />
        <meshStandardMaterial color={customization.eyeColor} />
      </mesh>
      
      {/* Accessories based on outfit */}
      {customization.outfit === 'cyberpunk' && (
        <>
          {/* Visor */}
          <mesh position={[0, 1.65, 0.1]}>
            <boxGeometry args={[0.2, 0.05, 0.02]} />
            <meshStandardMaterial color="#00ffff" transparent opacity={0.7} />
          </mesh>
          {/* Neon trim on body */}
          <mesh position={[0, 1, 0.26]}>
            <boxGeometry args={[0.5, 0.02, 0.02]} />
            <meshStandardMaterial color="#00ffff" emissive="#00ffff" emissiveIntensity={0.3} />
          </mesh>
        </>
      )}
      
      {customization.outfit === 'gaming' && (
        <>
          {/* Headset */}
          <mesh position={[0, 1.7, 0]}>
            <torusGeometry args={[0.2, 0.02, 8, 16]} />
            <meshStandardMaterial color="#9400D3" />
          </mesh>
        </>
      )}
    </group>
  )
}

export function SimpleAvatar3D({ 
  customization, 
  className = "",
  autoRotate = false 
}: SimpleAvatar3DProps) {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 3], fov: 50 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.6} />
        <directionalLight 
          position={[5, 5, 5]} 
          intensity={0.8}
          castShadow
        />
        <pointLight position={[-5, 5, 5]} intensity={0.3} color="#00ffff" />
        
        {/* Avatar Model */}
        <SimpleAvatarModel customization={customization} />
        
        {/* Camera controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          autoRotate={autoRotate}
          autoRotateSpeed={2}
          minDistance={1}
          maxDistance={10}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
        />
      </Canvas>
    </div>
  )
}

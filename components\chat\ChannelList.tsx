"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useChat } from '@/contexts/ChatContext'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Hash,
  MessageCircle,
  Plus,
  Search,
  Lock,
  Settings
} from 'lucide-react'
import { ChatChannel } from '@/types/chat'

interface ChannelListProps {
  activeTab: 'channels' | 'dms' | 'users'
  searchQuery: string
  onSearchChange: (query: string) => void
  className?: string
}

export function ChannelList({
  activeTab,
  searchQuery,
  onSearchChange,
  className = ""
}: ChannelListProps) {
  const { user: _user } = useAuth()
  const {
    channels,
    directMessages,
    onlineUsers,
    activeChannelId,
    activeDMId,
    setActiveChannel,
    setActiveDM,
    createChannel,
    startDirectMessage
  } = useChat()

  const [showCreateChannel, setShowCreateChannel] = useState(false)
  const [newChannelName, setNewChannelName] = useState('')
  const [newChannelDescription, setNewChannelDescription] = useState('')
  const [isPrivateChannel, setIsPrivateChannel] = useState(false)

  const filteredChannels = channels.filter(channel =>
    channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    channel.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const filteredUsers = onlineUsers.filter(user =>
    user.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.username.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleCreateChannel = async () => {
    if (!newChannelName.trim()) return

    try {
      await createChannel(newChannelName.trim(), newChannelDescription.trim(), isPrivateChannel)
      setNewChannelName('')
      setNewChannelDescription('')
      setIsPrivateChannel(false)
      setShowCreateChannel(false)
    } catch (error) {
      console.error('Error creating channel:', error)
    }
  }

  const handleStartDM = async (userId: string) => {
    try {
      const dmId = await startDirectMessage(userId)
      setActiveDM(dmId)
    } catch (error) {
      console.error('Error starting DM:', error)
    }
  }

  const getChannelIcon = (channel: ChatChannel) => {
    if (channel.type === 'safe_dm' || channel.type === 'team') {
      return <Lock className="w-4 h-4" />
    }
    return <Hash className="w-4 h-4" />
  }

  const getUserStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400'
      case 'away': return 'bg-yellow-400'
      case 'busy': return 'bg-red-400'
      default: return 'bg-gray-400'
    }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Search */}
      <div className="p-3 border-b border-gray-800/50">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder={`Search ${activeTab}...`}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400"
          />
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {activeTab === 'channels' && (
            <>
              {filteredChannels.map((channel) => (
                <motion.button
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors group ${
                    activeChannelId === channel.id
                      ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                      : 'text-gray-300 hover:bg-gray-800/50'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {getChannelIcon(channel)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium truncate">
                        {channel.name}
                      </span>
                      {channel.unreadCount > 0 && (
                        <Badge className="bg-red-500 text-white text-xs">
                          {channel.unreadCount > 99 ? '99+' : channel.unreadCount}
                        </Badge>
                      )}
                    </div>
                    {channel.description && (
                      <p className="text-xs text-gray-400 truncate mt-1">
                        {channel.description}
                      </p>
                    )}
                  </div>
                  <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white p-1 h-auto"
                    >
                      <Settings className="w-3 h-3" />
                    </Button>
                  </div>
                </motion.button>
              ))}

              {/* Create Channel Form */}
              {showCreateChannel && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="p-3 bg-gray-800/50 rounded-lg border border-gray-700 space-y-3"
                >
                  <Input
                    placeholder="Channel name"
                    value={newChannelName}
                    onChange={(e) => setNewChannelName(e.target.value)}
                    className="bg-gray-900/50 border-gray-600 text-white"
                  />
                  <Input
                    placeholder="Description (optional)"
                    value={newChannelDescription}
                    onChange={(e) => setNewChannelDescription(e.target.value)}
                    className="bg-gray-900/50 border-gray-600 text-white"
                  />
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="private-channel"
                      checked={isPrivateChannel}
                      onChange={(e) => setIsPrivateChannel(e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="private-channel" className="text-sm text-gray-300">
                      Private channel
                    </label>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={handleCreateChannel}
                      disabled={!newChannelName.trim()}
                      className="bg-cyan-500 hover:bg-cyan-600"
                    >
                      Create
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowCreateChannel(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </motion.div>
              )}
            </>
          )}

          {activeTab === 'dms' && (
            <>
              {directMessages.map((dm) => (
                <motion.button
                  key={dm.id}
                  onClick={() => setActiveDM(dm.id)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${
                    activeDMId === dm.id
                      ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                      : 'text-gray-300 hover:bg-gray-800/50'
                  }`}
                >
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      U
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium truncate">
                        Direct Message
                      </span>
                      {dm.unreadCount > 0 && (
                        <Badge className="bg-red-500 text-white text-xs">
                          {dm.unreadCount > 99 ? '99+' : dm.unreadCount}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-gray-400 truncate">
                      Last message...
                    </p>
                  </div>
                </motion.button>
              ))}
            </>
          )}

          {activeTab === 'users' && (
            <>
              {filteredUsers.map((chatUser) => (
                <motion.div
                  key={chatUser.id}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center gap-3 p-3 rounded-lg text-gray-300 hover:bg-gray-800/50 transition-colors group"
                >
                  <div className="relative">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={chatUser.avatar_url} />
                      <AvatarFallback className="text-xs bg-gradient-to-r from-cyan-500 to-purple-500 text-white">
                        {chatUser.displayName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-900 ${getUserStatusColor(chatUser.status)}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="text-sm font-medium truncate block">
                      {chatUser.displayName}
                    </span>
                    <span className="text-xs text-gray-400 truncate block">
                      @{chatUser.username}
                    </span>
                  </div>
                  <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleStartDM(chatUser.id)}
                      className="text-gray-400 hover:text-white p-1 h-auto"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </>
          )}
        </div>
      </ScrollArea>

      {/* Create Button */}
      <div className="p-3 border-t border-gray-800/50">
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            if (activeTab === 'channels') {
              setShowCreateChannel(true)
            }
          }}
          className="w-full border-gray-700 text-gray-300 hover:bg-gray-800"
        >
          <Plus className="w-4 h-4 mr-2" />
          {activeTab === 'channels' ? 'New Channel' : 
           activeTab === 'dms' ? 'New Chat' : 'Invite User'}
        </Button>
      </div>
    </div>
  )
}

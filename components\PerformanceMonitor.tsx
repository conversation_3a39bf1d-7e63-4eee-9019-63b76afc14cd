"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Activity, MemoryStick, Zap, Eye, EyeOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { PerformanceMonitor as PerfMonitor } from '@/utils/threeCleanup'

interface PerformanceData {
  fps: number
  memory: number
  renderTime: number
  triangles: number
  drawCalls: number
  qualityLevel: 'low' | 'medium' | 'high'
}

interface PerformanceMonitorProps {
  className?: string
  showDetailed?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export function PerformanceMonitor({ 
  className = "", 
  showDetailed = false,
  position = 'top-right'
}: PerformanceMonitorProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    fps: 60,
    memory: 0,
    renderTime: 16.67,
    triangles: 0,
    drawCalls: 0,
    qualityLevel: 'high'
  })
  
  const perfMonitor = useRef(new PerfMonitor())
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  const renderTimes = useRef<number[]>([])

  // Get draw call count (approximation)
  const getDrawCallCount = useCallback((): number => {
    // This would need access to the Three.js renderer
    // For now, return an estimated value based on scene complexity
    return Math.floor(performanceData.triangles / 1000) + 10
  }, [performanceData.triangles])

  // Performance monitoring loop
  useEffect(() => {
    let animationFrame: number

    const updatePerformance = () => {
      const currentTime = performance.now()
      const deltaTime = currentTime - lastTime.current
      
      frameCount.current++
      renderTimes.current.push(deltaTime)
      
      // Keep only last 60 frames for average calculation
      if (renderTimes.current.length > 60) {
        renderTimes.current.shift()
      }

      // Update every second
      if (deltaTime >= 1000) {
        const avgRenderTime = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length
        const fps = Math.round(1000 / avgRenderTime)
        
        const perfData = perfMonitor.current.update()
        
        setPerformanceData({
          fps: fps,
          memory: perfData.memory,
          renderTime: avgRenderTime,
          triangles: getTriangleCount(),
          drawCalls: getDrawCallCount(),
          qualityLevel: perfMonitor.current.getQualityLevel()
        })

        frameCount.current = 0
        lastTime.current = currentTime
        renderTimes.current = []
      }

      animationFrame = requestAnimationFrame(updatePerformance)
    }

    animationFrame = requestAnimationFrame(updatePerformance)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [])

  // Get triangle count from WebGL context
  const getTriangleCount = (): number => {
    try {
      const canvas = document.querySelector('canvas')
      if (!canvas) return 0
      
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
      if (!gl) return 0
      
      // This is an approximation - actual triangle count would need renderer access
      return Math.floor(Math.random() * 10000) // Placeholder
    } catch {
      return 0
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      default:
        return 'top-4 right-4'
    }
  }

  const getFPSColor = (fps: number) => {
    if (fps >= 50) return 'text-green-400'
    if (fps >= 30) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'high': return 'bg-green-500/20 text-green-400 border-green-500/40'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/40'
      case 'low': return 'bg-red-500/20 text-red-400 border-red-500/40'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/40'
    }
  }

  // Keyboard shortcut to toggle visibility
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        setIsVisible(!isVisible)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isVisible])

  return (
    <div className={`fixed z-50 ${getPositionClasses()} ${className}`}>
      {/* Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="mb-2 bg-black/50 border-gray-600 hover:bg-black/70"
      >
        {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
      </Button>

      {/* Performance Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="bg-black/80 border-gray-600 backdrop-blur-sm min-w-[200px]">
              <CardContent className="p-3 space-y-2">
                {/* FPS Display */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-blue-400" />
                    <span className="text-sm text-gray-300">FPS</span>
                  </div>
                  <span className={`text-sm font-mono ${getFPSColor(performanceData.fps)}`}>
                    {performanceData.fps}
                  </span>
                </div>

                {/* Quality Level */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-purple-400" />
                    <span className="text-sm text-gray-300">Quality</span>
                  </div>
                  <Badge className={getQualityColor(performanceData.qualityLevel)}>
                    {performanceData.qualityLevel.toUpperCase()}
                  </Badge>
                </div>

                {/* Memory Usage */}
                {performanceData.memory > 0 && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MemoryStick className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-gray-300">Memory</span>
                    </div>
                    <span className="text-sm font-mono text-gray-300">
                      {performanceData.memory.toFixed(1)}MB
                    </span>
                  </div>
                )}

                {/* Detailed Information */}
                {showDetailed && (
                  <>
                    <hr className="border-gray-600" />
                    
                    {/* Render Time */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">Render Time</span>
                      <span className="text-xs font-mono text-gray-300">
                        {performanceData.renderTime.toFixed(2)}ms
                      </span>
                    </div>

                    {/* Triangle Count */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">Triangles</span>
                      <span className="text-xs font-mono text-gray-300">
                        {performanceData.triangles.toLocaleString()}
                      </span>
                    </div>

                    {/* Draw Calls */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">Draw Calls</span>
                      <span className="text-xs font-mono text-gray-300">
                        {performanceData.drawCalls}
                      </span>
                    </div>
                  </>
                )}

                {/* Keyboard Shortcut Hint */}
                <div className="text-xs text-gray-500 text-center pt-1 border-t border-gray-600">
                  Ctrl+Shift+P to toggle
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default PerformanceMonitor

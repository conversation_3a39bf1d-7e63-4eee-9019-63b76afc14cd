"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { LandingNavigation } from './LandingNavigation'
import { Safe3DComponent } from './ErrorBoundary'

import { CommunityHighlightsSection } from './sections/CommunityHighlightsSection'
import { TestimonialsSection } from './sections/TestimonialsSection'
import { CTASection } from './sections/CTASection'
// Temporarily using simple fallbacks instead of complex 3D components
import { Footer } from './sections/Footer'
import { SignupModal } from './SignupModal'
import { Dialog } from '@/components/ui/dialog'
import { useLandingPageState } from '../hooks/useLandingPageState'

interface LandingPageProps {
  onLogin: () => void
}

export function LandingPage({ onLogin }: LandingPageProps) {
  const {
    currentTestimonial,
    setCurrentTestimonial,
    showSignupModal,
    setShowSignupModal,
    scrollProgress,
    showFloatingCTA: _showFloatingCTA,
    signupData,
    setSignupData,
    showChatAssistant: _showChatAssistant,
    setShowChatAssistant: _setShowChatAssistant,
    chatMessages: _chatMessages,
    chatInput: _chatInput,
    setChatInput: _setChatInput,
    emailReminder: _emailReminder,
    setEmailReminder: _setEmailReminder,
    byteMentorMessage: _byteMentorMessage,
    showByteMentor: _showByteMentor,
    setShowByteMentor: _setShowByteMentor,
    handleSignup,
    toggleInterest,
    handleChatSubmit: _handleChatSubmit,
    handleEmailReminder: _handleEmailReminder,
  } = useLandingPageState(onLogin)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-blue-900 text-white overflow-x-hidden">
      {/* Progress indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 z-50 origin-left"
        style={{ scaleX: scrollProgress }}
      />

      <div className="relative z-10">
        {/* Navigation */}
        <LandingNavigation onLogin={onLogin} />

        {/* Quantum Arrival Hero Section */}
        <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-space-dark via-space-blue to-space-dark text-white relative overflow-hidden">
          <div className="container mx-auto px-6 text-center z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto"
            >
              <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent">
                NanoHero
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-300">
                Where Young Minds Become Digital Heroes
              </p>
              <motion.button
                onClick={() => {
                  const element = document.querySelector('#decode')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
                className="bg-gradient-to-r from-neural-cyan to-quantum-purple px-8 py-4 rounded-full text-lg font-semibold hover:scale-105 transition-transform"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Start Your Journey
              </motion.button>
            </motion.div>
          </div>

          {/* Background Animation */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-neural-cyan rounded-full animate-pulse"></div>
            <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-quantum-purple rounded-full animate-pulse delay-1000"></div>
            <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-quantum-gold rounded-full animate-pulse delay-2000"></div>
          </div>
        </section>

        {/* Alternative Hero Section */}
        {/* <HeroSection onScrollToFeatures={() => {
          const element = document.querySelector('#features')
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }} /> */}

        {/* DeCode Learning Section */}
        <section id="decode" className="py-20 bg-gradient-to-br from-gray-900 to-gray-800">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">DeCode Learning</h2>
              <p className="text-xl text-gray-300">Master the fundamentals of digital literacy</p>
            </div>
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-semibold text-white mb-4">Learn by Doing</h3>
                <p className="text-gray-300 mb-6">
                  Interactive tutorials that teach coding, cybersecurity, and digital citizenship
                  through hands-on projects and real-world scenarios.
                </p>
                <button
                  onClick={() => setShowSignupModal(true)}
                  className="bg-gradient-to-r from-neural-cyan to-quantum-purple px-6 py-3 rounded-lg text-white font-semibold hover:scale-105 transition-transform"
                >
                  Start Learning
                </button>
              </div>
              <div className="bg-gray-800 p-8 rounded-lg">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-neural-cyan rounded-full flex items-center justify-center">
                      <span className="text-sm">📚</span>
                    </div>
                    <span className="text-white">Interactive Tutorials</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-quantum-purple rounded-full flex items-center justify-center">
                      <span className="text-sm">🔒</span>
                    </div>
                    <span className="text-white">Safe Environment</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-quantum-gold rounded-full flex items-center justify-center">
                      <span className="text-sm">🏆</span>
                    </div>
                    <span className="text-white">Achievement System</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Game-Like Journey Trail */}
        <section className="py-20 bg-gray-800">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">Your Learning Journey</h2>
            <p className="text-xl text-gray-300 mb-12">Progress through levels and unlock new skills</p>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="w-16 h-16 bg-neural-cyan rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Beginner</h3>
                <p className="text-gray-300 text-sm">Start your digital journey</p>
              </div>
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="w-16 h-16 bg-quantum-purple rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Explorer</h3>
                <p className="text-gray-300 text-sm">Discover new technologies</p>
              </div>
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="w-16 h-16 bg-quantum-gold rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Creator</h3>
                <p className="text-gray-300 text-sm">Build amazing projects</p>
              </div>
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="w-16 h-16 bg-gradient-to-r from-neural-cyan to-quantum-purple rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">👑</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Hero</h3>
                <p className="text-gray-300 text-sm">Master digital skills</p>
              </div>
            </div>
          </div>
        </section>

        {/* Features Showcase */}
        <section id="features" className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Explore NanoHero Features</h2>
              <p className="text-xl text-gray-300">Safe, educational, and fun digital experiences</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-neural-cyan rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-2xl">🎮</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Gamer Zone</h3>
                <p className="text-gray-300">Learn through gaming with educational challenges</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-quantum-purple rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Hack Lab</h3>
                <p className="text-gray-300">Ethical cybersecurity in a safe environment</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-quantum-gold rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-2xl">🎨</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Creator Studio</h3>
                <p className="text-gray-300">Build and share your digital creations</p>
              </div>
            </div>
          </div>
        </section>

        {/* Digital Trust Section */}
        <section className="py-20 bg-gradient-to-br from-gray-900 to-gray-800">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Digital Safety First</h2>
              <p className="text-xl text-gray-300">Learn to navigate the digital world safely</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-gray-800 p-6 rounded-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Privacy Protection</h3>
                <p className="text-gray-300">Learn to protect your personal information online</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">👥</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Digital Citizenship</h3>
                <p className="text-gray-300">Understand responsible online behavior</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Cybersecurity</h3>
                <p className="text-gray-300">Master the basics of staying secure online</p>
              </div>
            </div>
          </div>
        </section>

        {/* Community Highlights */}
        <CommunityHighlightsSection />

        {/* Call to Adventure Quest */}
        <section className="py-20 bg-gradient-to-r from-neural-cyan to-quantum-purple">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">Ready for Your Quest?</h2>
            <p className="text-xl text-white/80 mb-8">Join thousands of young digital heroes</p>
            <button
              onClick={() => setShowSignupModal(true)}
              className="bg-white text-gray-900 px-8 py-4 rounded-full text-lg font-semibold hover:scale-105 transition-transform"
            >
              Start Your Adventure
            </button>
          </div>
        </section>

        {/* AI Sidekick Byte Section */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Meet Your AI Sidekick</h2>
              <p className="text-xl text-gray-300">Get personalized help on your learning journey</p>
            </div>
            <div className="max-w-2xl mx-auto bg-gray-800 p-8 rounded-lg">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-neural-cyan to-quantum-purple rounded-full flex items-center justify-center">
                  <span className="text-xl">🤖</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-2">ByteBuddy</h3>
                  <p className="text-gray-300 mb-4">
                    "Hi there! I'm ByteBuddy, your personal AI learning companion. I'm here to help you
                    navigate your digital learning journey, answer questions, and celebrate your achievements!"
                  </p>
                  <button
                    onClick={() => setShowSignupModal(true)}
                    className="bg-gradient-to-r from-neural-cyan to-quantum-purple px-6 py-2 rounded-lg text-white font-semibold"
                  >
                    Chat with ByteBuddy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <TestimonialsSection
          currentTestimonial={currentTestimonial}
          setCurrentTestimonial={setCurrentTestimonial}
        />

        {/* CTA Section */}
        <CTASection onSignupClick={() => setShowSignupModal(true)} />

        {/* Final CTA */}
        {/* <FinalCTASection
          showSignupModal={showSignupModal}
          setShowSignupModal={setShowSignupModal}
        /> */}

        {/* Footer */}
        <Footer />
      </div>

      {/* Floating CTA */}
      {/* {showFloatingCTA && (
        <FloatingCTA onSignupClick={() => setShowSignupModal(true)} />
      )} */}

      {/* ByteMentor Chat */}
      {/* <ByteMentorChat
        message={byteMentorMessage}
        showByteMentor={showByteMentor}
        setShowByteMentor={setShowByteMentor}
        showChatAssistant={showChatAssistant}
        setShowChatAssistant={setShowChatAssistant}
        chatMessages={chatMessages}
        chatInput={chatInput}
        setChatInput={setChatInput}
        emailReminder={emailReminder}
        setEmailReminder={setEmailReminder}
        onChatSubmit={handleChatSubmit}
        onEmailReminder={handleEmailReminder}
      /> */}

      {/* Signup Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <SignupModal
          isOpen={showSignupModal}
          onClose={() => setShowSignupModal(false)}
          signupData={signupData}
          setSignupData={setSignupData}
          onSignup={handleSignup}
          onToggleInterest={toggleInterest}
        />
      </Dialog>
    </div>
  )
}

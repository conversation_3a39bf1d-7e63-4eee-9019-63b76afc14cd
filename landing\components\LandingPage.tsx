"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { LandingNavigation } from './LandingNavigation'
import { Safe3DComponent } from './ErrorBoundary'
import { FeaturesShowcaseSection } from './sections/FeaturesShowcaseSection'
import { CommunityHighlightsSection } from './sections/CommunityHighlightsSection'
import { TestimonialsSection } from './sections/TestimonialsSection'
import { CTASection } from './sections/CTASection'
// Import components directly for now to debug missing sections
import { QuantumArrivalHero } from './sections/QuantumArrivalHero'
import { DeCodeLearningSection } from './sections/DeCodeLearningSection'
import { GameLikeJourneyTrail } from './sections/GameLikeJourneyTrail'
import { DigitalTrustSection } from './sections/DigitalTrustSection'
import { InteractiveByteverseMap } from './sections/InteractiveByteverseMap'
import { CallToAdventureQuest } from './sections/CallToAdventureQuest'
import { AISidekickByteSection } from './sections/AISidekickByteSection'
import { Footer } from './sections/Footer'
import { SignupModal } from './SignupModal'
import { Dialog } from '@/components/ui/dialog'
import { useLandingPageState } from '../hooks/useLandingPageState'

interface LandingPageProps {
  onLogin: () => void
}

export function LandingPage({ onLogin }: LandingPageProps) {
  const {
    currentTestimonial,
    setCurrentTestimonial,
    showSignupModal,
    setShowSignupModal,
    scrollProgress,
    showFloatingCTA: _showFloatingCTA,
    signupData,
    setSignupData,
    showChatAssistant: _showChatAssistant,
    setShowChatAssistant: _setShowChatAssistant,
    chatMessages: _chatMessages,
    chatInput: _chatInput,
    setChatInput: _setChatInput,
    emailReminder: _emailReminder,
    setEmailReminder: _setEmailReminder,
    byteMentorMessage: _byteMentorMessage,
    showByteMentor: _showByteMentor,
    setShowByteMentor: _setShowByteMentor,
    handleSignup,
    toggleInterest,
    handleChatSubmit: _handleChatSubmit,
    handleEmailReminder: _handleEmailReminder,
  } = useLandingPageState(onLogin)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-blue-900 text-white overflow-x-hidden">
      {/* Progress indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 z-50 origin-left"
        style={{ scaleX: scrollProgress }}
      />

      <div className="relative z-10">
        {/* Navigation */}
        <LandingNavigation onLogin={onLogin} />

        {/* Quantum Arrival Hero Section */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
            <QuantumArrivalHero onScrollToNext={() => {
              const element = document.querySelector('#decode')
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' })
              }
            }} />
          </React.Suspense>
        </Safe3DComponent>

        {/* Alternative Hero Section */}
        {/* <HeroSection onScrollToFeatures={() => {
          const element = document.querySelector('#features')
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }} /> */}

        {/* DeCode Learning Section */}
        <section id="decode">
          <Safe3DComponent>
            <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
              <DeCodeLearningSection onSignupClick={() => setShowSignupModal(true)} />
            </React.Suspense>
          </Safe3DComponent>
        </section>

        {/* Game-Like Journey Trail */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <GameLikeJourneyTrail onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent>

        {/* Features Showcase */}
        <section id="features">
          <FeaturesShowcaseSection onSignupClick={() => setShowSignupModal(true)} />
        </section>

        {/* Digital Trust Section */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <DigitalTrustSection />
          </React.Suspense>
        </Safe3DComponent>

        {/* Interactive Byteverse Map */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <InteractiveByteverseMap onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent>

        {/* Community Highlights */}
        <CommunityHighlightsSection />

        {/* Call to Adventure Quest */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <CallToAdventureQuest onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent>

        {/* AI Sidekick Byte Section */}
        <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <AISidekickByteSection onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent>

        {/* Testimonials */}
        <TestimonialsSection
          currentTestimonial={currentTestimonial}
          setCurrentTestimonial={setCurrentTestimonial}
        />

        {/* CTA Section */}
        <CTASection onSignupClick={() => setShowSignupModal(true)} />

        {/* Final CTA */}
        {/* <FinalCTASection
          showSignupModal={showSignupModal}
          setShowSignupModal={setShowSignupModal}
        /> */}

        {/* Footer */}
        <Footer />
      </div>

      {/* Floating CTA */}
      {/* {showFloatingCTA && (
        <FloatingCTA onSignupClick={() => setShowSignupModal(true)} />
      )} */}

      {/* ByteMentor Chat */}
      {/* <ByteMentorChat
        message={byteMentorMessage}
        showByteMentor={showByteMentor}
        setShowByteMentor={setShowByteMentor}
        showChatAssistant={showChatAssistant}
        setShowChatAssistant={setShowChatAssistant}
        chatMessages={chatMessages}
        chatInput={chatInput}
        setChatInput={setChatInput}
        emailReminder={emailReminder}
        setEmailReminder={setEmailReminder}
        onChatSubmit={handleChatSubmit}
        onEmailReminder={handleEmailReminder}
      /> */}

      {/* Signup Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <SignupModal
          isOpen={showSignupModal}
          onClose={() => setShowSignupModal(false)}
          signupData={signupData}
          setSignupData={setSignupData}
          onSignup={handleSignup}
          onToggleInterest={toggleInterest}
        />
      </Dialog>
    </div>
  )
}

"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { LandingNavigation } from './LandingNavigation'
import { Safe3DComponent } from './ErrorBoundary'
import { FeaturesShowcaseSection } from './sections/FeaturesShowcaseSection'
import { CommunityHighlightsSection } from './sections/CommunityHighlightsSection'
import { TestimonialsSection } from './sections/TestimonialsSection'
import { CTASection } from './sections/CTASection'
// Lazy load heavy 3D components for better performance
const QuantumArrivalHero = React.lazy(() => import('./sections/QuantumArrivalHero').then(m => ({ default: m.QuantumArrivalHero })))
const DeCodeLearningSection = React.lazy(() => import('./sections/DeCodeLearningSection').then(m => ({ default: m.DeCodeLearningSection })))
const GameLikeJourneyTrail = React.lazy(() => import('./sections/GameLikeJourneyTrail').then(m => ({ default: m.GameLikeJourneyTrail })))
const DigitalTrustSection = React.lazy(() => import('./sections/DigitalTrustSection').then(m => ({ default: m.DigitalTrustSection })))
const InteractiveByteverseMap = React.lazy(() => import('./sections/InteractiveByteverseMap').then(m => ({ default: m.InteractiveByteverseMap })))
const CallToAdventureQuest = React.lazy(() => import('./sections/CallToAdventureQuest').then(m => ({ default: m.CallToAdventureQuest })))
const AISidekickByteSection = React.lazy(() => import('./sections/AISidekickByteSection').then(m => ({ default: m.AISidekickByteSection })))
import { Footer } from './sections/Footer'
import { SignupModal } from './SignupModal'
import { Dialog } from '@/components/ui/dialog'
import { useLandingPageState } from '../hooks/useLandingPageState'

interface LandingPageProps {
  onLogin: () => void
}

export function LandingPage({ onLogin }: LandingPageProps) {
  const {
    currentTestimonial,
    setCurrentTestimonial,
    showSignupModal,
    setShowSignupModal,
    scrollProgress,
    showFloatingCTA: _showFloatingCTA,
    signupData,
    setSignupData,
    showChatAssistant: _showChatAssistant,
    setShowChatAssistant: _setShowChatAssistant,
    chatMessages: _chatMessages,
    chatInput: _chatInput,
    setChatInput: _setChatInput,
    emailReminder: _emailReminder,
    setEmailReminder: _setEmailReminder,
    byteMentorMessage: _byteMentorMessage,
    showByteMentor: _showByteMentor,
    setShowByteMentor: _setShowByteMentor,
    handleSignup,
    toggleInterest,
    handleChatSubmit: _handleChatSubmit,
    handleEmailReminder: _handleEmailReminder,
  } = useLandingPageState(onLogin)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-blue-900 text-white overflow-x-hidden">
      {/* Progress indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 z-50 origin-left"
        style={{ scaleX: scrollProgress }}
      />

      <div className="relative z-10">
        {/* Navigation */}
        <LandingNavigation onLogin={onLogin} />

        {/* Quantum Arrival Hero Section */}
        <Safe3DComponent>
          <QuantumArrivalHero onScrollToNext={() => {
            const element = document.querySelector('#decode')
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' })
            }
          }} />
        </Safe3DComponent>

        {/* Alternative Hero Section */}
        {/* <HeroSection onScrollToFeatures={() => {
          const element = document.querySelector('#features')
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }} /> */}

        {/* DeCode Learning Section */}
        <section id="decode">
          <Safe3DComponent>
            <DeCodeLearningSection onSignupClick={() => setShowSignupModal(true)} />
          </Safe3DComponent>
        </section>

        {/* Game-Like Journey Trail */}
        <Safe3DComponent>
          <GameLikeJourneyTrail onSignupClick={() => setShowSignupModal(true)} />
        </Safe3DComponent>

        {/* Features Showcase */}
        <section id="features">
          <FeaturesShowcaseSection onSignupClick={() => setShowSignupModal(true)} />
        </section>

        {/* Digital Trust Section */}
        <Safe3DComponent>
          <DigitalTrustSection />
        </Safe3DComponent>

        {/* Interactive Byteverse Map */}
        <Safe3DComponent>
          <InteractiveByteverseMap onSignupClick={() => setShowSignupModal(true)} />
        </Safe3DComponent>

        {/* Community Highlights */}
        <CommunityHighlightsSection />

        {/* Call to Adventure Quest */}
        <Safe3DComponent>
          <CallToAdventureQuest onSignupClick={() => setShowSignupModal(true)} />
        </Safe3DComponent>

        {/* AI Sidekick Byte Section */}
        <Safe3DComponent>
          <AISidekickByteSection onSignupClick={() => setShowSignupModal(true)} />
        </Safe3DComponent>

        {/* Testimonials */}
        <TestimonialsSection
          currentTestimonial={currentTestimonial}
          setCurrentTestimonial={setCurrentTestimonial}
        />

        {/* CTA Section */}
        <CTASection onSignupClick={() => setShowSignupModal(true)} />

        {/* Final CTA */}
        {/* <FinalCTASection
          showSignupModal={showSignupModal}
          setShowSignupModal={setShowSignupModal}
        /> */}

        {/* Footer */}
        <Footer />
      </div>

      {/* Floating CTA */}
      {/* {showFloatingCTA && (
        <FloatingCTA onSignupClick={() => setShowSignupModal(true)} />
      )} */}

      {/* ByteMentor Chat */}
      {/* <ByteMentorChat
        message={byteMentorMessage}
        showByteMentor={showByteMentor}
        setShowByteMentor={setShowByteMentor}
        showChatAssistant={showChatAssistant}
        setShowChatAssistant={setShowChatAssistant}
        chatMessages={chatMessages}
        chatInput={chatInput}
        setChatInput={setChatInput}
        emailReminder={emailReminder}
        setEmailReminder={setEmailReminder}
        onChatSubmit={handleChatSubmit}
        onEmailReminder={handleEmailReminder}
      /> */}

      {/* Signup Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <SignupModal
          isOpen={showSignupModal}
          onClose={() => setShowSignupModal(false)}
          signupData={signupData}
          setSignupData={setSignupData}
          onSignup={handleSignup}
          onToggleInterest={toggleInterest}
        />
      </Dialog>
    </div>
  )
}

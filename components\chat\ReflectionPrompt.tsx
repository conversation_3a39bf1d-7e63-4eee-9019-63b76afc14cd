"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Brain, 
  Heart, 
  Sparkles, 
  CheckCircle,
  Coins,
  Zap,
  X
} from 'lucide-react'
import { ReflectionPrompt as ReflectionPromptType } from '@/types/chat'
import { calculateReflectionImpact } from '@/data/reflectionPrompts'

interface ReflectionPromptProps {
  prompt: ReflectionPromptType
  onSubmitResponse: (responseIndex: number, response: string) => void
  onSkip: () => void
  className?: string
}

const promptTypeConfig = {
  post_chat: {
    icon: Heart,
    title: 'Chat Reflection',
    color: 'from-pink-500 to-rose-500',
    description: 'Think about your recent conversations'
  },
  daily: {
    icon: Sparkles,
    title: 'Daily Intention',
    color: 'from-yellow-500 to-orange-500',
    description: 'Set your communication goals for today'
  },
  conflict_resolution: {
    icon: Brain,
    title: 'Wisdom Moment',
    color: 'from-purple-500 to-indigo-500',
    description: 'Learn about handling disagreements'
  },
  kindness_check: {
    icon: Heart,
    title: 'Kindness Check',
    color: 'from-green-500 to-emerald-500',
    description: 'Reflect on spreading positivity'
  }
}

export function ReflectionPrompt({
  prompt,
  onSubmitResponse,
  onSkip,
  className = ""
}: ReflectionPromptProps) {
  const [selectedResponse, setSelectedResponse] = useState<number | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showReward, setShowReward] = useState(false)

  const config = promptTypeConfig[prompt.type]
  const Icon = config.icon

  const handleSubmit = async () => {
    if (selectedResponse === null) return

    setIsSubmitting(true)
    
    try {
      const response = prompt.responses[selectedResponse]
      const _impact = calculateReflectionImpact(prompt, response, selectedResponse)
      
      // Show reward animation
      setShowReward(true)
      
      // Wait for animation then submit
      setTimeout(() => {
        onSubmitResponse(selectedResponse, response)
      }, 2000)
      
    } catch (error) {
      console.error('Error submitting reflection:', error)
      setIsSubmitting(false)
    }
  }

  if (showReward) {
    const impact = calculateReflectionImpact(
      prompt, 
      prompt.responses[selectedResponse!], 
      selectedResponse!
    )

    return (
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className={`max-w-md mx-auto ${className}`}
      >
        <Card className="bg-black/95 backdrop-blur-xl border-gray-800/50 overflow-hidden">
          <CardContent className="p-8 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className={`w-20 h-20 mx-auto mb-4 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center`}
            >
              <CheckCircle className="w-10 h-10 text-white" />
            </motion.div>

            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl font-bold text-white mb-2 font-space-grotesk"
            >
              Reflection Complete!
            </motion.h3>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="text-gray-400 mb-6"
            >
              Thank you for taking time to reflect on your communication
            </motion.p>

            {/* Rewards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="flex justify-center gap-4 mb-6"
            >
              {impact.ceBonus > 0 && (
                <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30">
                  <Zap className="w-3 h-3 mr-1" />
                  +{impact.ceBonus} CE
                </Badge>
              )}
              {impact.cubitsBonus > 0 && (
                <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                  <Coins className="w-3 h-3 mr-1" />
                  +{impact.cubitsBonus} Cubits
                </Badge>
              )}
              {impact.harmonyBonus > 0 && (
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  +{impact.harmonyBonus} Harmony
                </Badge>
              )}
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.0 }}
              className="text-xs text-gray-500"
            >
              Your thoughtful reflection helps build a better community ✨
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`max-w-md mx-auto ${className}`}
    >
      <Card className="bg-black/95 backdrop-blur-xl border-gray-800/50">
        <CardHeader className="text-center">
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center`}>
              <Icon className="w-6 h-6 text-white" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onSkip}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          <CardTitle className="text-lg font-bold text-white font-space-grotesk">
            {config.title}
          </CardTitle>
          <p className="text-sm text-gray-400">
            {config.description}
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Question */}
          <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
            <p className="text-white font-medium">
              {prompt.question}
            </p>
          </div>

          {/* Response Options */}
          <div className="space-y-3">
            {prompt.responses.map((response, index) => (
              <motion.button
                key={index}
                onClick={() => setSelectedResponse(index)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`w-full p-4 rounded-lg border text-left transition-all ${
                  selectedResponse === index
                    ? 'border-cyan-500/50 bg-cyan-500/10 text-cyan-400'
                    : 'border-gray-700 bg-gray-800/30 text-gray-300 hover:border-gray-600 hover:bg-gray-700/30'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    selectedResponse === index
                      ? 'border-cyan-400 bg-cyan-400'
                      : 'border-gray-500'
                  }`}>
                    {selectedResponse === index && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                  <span className="text-sm">{response}</span>
                </div>
              </motion.button>
            ))}
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onSkip}
              className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              Skip for Now
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={selectedResponse === null || isSubmitting}
              className={`flex-1 bg-gradient-to-r ${config.color} text-white`}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Reflecting...
                </div>
              ) : (
                <>
                  <Brain className="w-4 h-4 mr-2" />
                  Submit Reflection
                </>
              )}
            </Button>
          </div>

          {/* Encouragement */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              💭 Taking time to reflect helps you grow as a communicator
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

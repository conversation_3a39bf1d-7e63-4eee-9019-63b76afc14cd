"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Bot, X, Send, Mail } from 'lucide-react'
import { byteMentorMessages } from '../../data/constants'
import { ChatMessage } from '../../hooks/useLandingPageState'

interface ByteMentorChatProps {
  byteMentorMessage: number
  showByteMentor: boolean
  setShowByteMentor: (show: boolean) => void
  showChatAssistant: boolean
  setShowChatAssistant: (show: boolean) => void
  chatMessages: ChatMessage[]
  chatInput: string
  setChatInput: (input: string) => void
  emailReminder: string
  setEmailReminder: (email: string) => void
  onChatSubmit: (e: React.FormEvent) => void
  onEmailReminder: () => void
}

export function ByteMentorChat({
  byteMentorMessage,
  showByteMentor,
  setShowByteMentor,
  showChatAssistant,
  setShowChatAssistant,
  chatMessages,
  chatInput,
  setChatInput,
  emailReminder,
  setEmailReminder,
  onChatSubmit,
  onEmailReminder
}: ByteMentorChatProps) {
  return (
    <>
      {/* ByteMentor Floating Assistant */}
      <AnimatePresence>
        {showByteMentor && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-6 left-6 z-40"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl p-4 shadow-2xl max-w-xs cursor-pointer"
              onClick={() => setShowChatAssistant(true)}
            >
              <div className="flex items-center gap-3 mb-2">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center"
                >
                  <Bot className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <div className="text-white font-semibold text-sm">ByteMentor</div>
                  <div className="text-white/70 text-xs">Your AI Guide</div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowByteMentor(false)
                  }}
                  className="text-white/70 hover:text-white ml-auto"
                >
                  <X className="w-4 h-4" />
                </motion.button>
              </div>
              <p className="text-white text-sm">
                {byteMentorMessages[byteMentorMessage]}
              </p>
              <div className="mt-2 text-xs text-white/70">
                Click to chat with me! 💬
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Assistant Modal */}
      <AnimatePresence>
        {showChatAssistant && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowChatAssistant(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-md"
            >
              <Card className="bg-black/90 border-gray-800 text-white">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <motion.div
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center"
                    >
                      <Bot className="w-5 h-5 text-white" />
                    </motion.div>
                    ByteMentor Assistant
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setShowChatAssistant(false)}
                      className="text-gray-400 hover:text-white ml-auto"
                    >
                      <X className="w-5 h-5" />
                    </motion.button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Chat Messages */}
                  <div className="h-64 overflow-y-auto space-y-3 p-3 bg-gray-900/50 rounded-lg">
                    <div className="flex gap-2">
                      <Bot className="w-6 h-6 text-purple-400 flex-shrink-0 mt-1" />
                      <div className="bg-purple-500/20 rounded-lg p-3 flex-1">
                        <p className="text-sm">
                          Hi! I&apos;m ByteMentor, your AI guide. Ask me anything about NanoHero or let me know if you&apos;d like a reminder to join later!
                        </p>
                      </div>
                    </div>
                    
                    {chatMessages.map((msg, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`flex gap-2 ${msg.isBot ? '' : 'flex-row-reverse'}`}
                      >
                        {msg.isBot ? (
                          <Bot className="w-6 h-6 text-purple-400 flex-shrink-0 mt-1" />
                        ) : (
                          <div className="w-6 h-6 bg-cyan-500 rounded-full flex-shrink-0 mt-1" />
                        )}
                        <div className={`rounded-lg p-3 flex-1 ${
                          msg.isBot 
                            ? 'bg-purple-500/20' 
                            : 'bg-cyan-500/20'
                        }`}>
                          <p className="text-sm">{msg.text}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Chat Input */}
                  <form onSubmit={onChatSubmit} className="flex gap-2">
                    <Input
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      placeholder="Ask me anything about NanoHero..."
                      className="bg-gray-900/50 border-gray-700 text-white"
                    />
                    <Button type="submit" size="icon" className="bg-purple-500 hover:bg-purple-600">
                      <Send className="w-4 h-4" />
                    </Button>
                  </form>

                  {/* Email Reminder */}
                  <div className="border-t border-gray-700 pt-4">
                    <p className="text-sm text-gray-400 mb-2">
                      Want a reminder to join NanoHero later?
                    </p>
                    <div className="flex gap-2">
                      <Input
                        value={emailReminder}
                        onChange={(e) => setEmailReminder(e.target.value)}
                        placeholder="Enter your email..."
                        type="email"
                        className="bg-gray-900/50 border-gray-700 text-white"
                      />
                      <Button 
                        onClick={onEmailReminder}
                        size="icon" 
                        className="bg-cyan-500 hover:bg-cyan-600"
                      >
                        <Mail className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

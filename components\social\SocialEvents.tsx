"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Video,
  Trophy,
  BookOpen,
  Coffee,
  Presentation,
  Plus,
  Search,
  ExternalLink,
  UserPlus,
  UserMinus,
  Play,
  Pause
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  SocialEvent, 
  User as UserType 
} from '@/types/social'

interface SocialEventsProps {
  currentUser: UserType
  events: SocialEvent[]
  onCreateEvent: (eventData: Partial<SocialEvent>) => void
  onJoinEvent: (eventId: string) => void
  onLeaveEvent: (eventId: string) => void
  onJoinWaitlist: (eventId: string) => void
  onStartEvent: (eventId: string) => void
  className?: string
}

export function SocialEvents({
  currentUser,
  events,
  onCreateEvent,
  onJoinEvent,
  onLeaveEvent,
  onJoinWaitlist,
  onStartEvent,
  className = ""
}: SocialEventsProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<SocialEvent | null>(null)

  const now = new Date()
  const upcomingEvents = events.filter(event => event.startTime > now && event.status === 'upcoming')
  const liveEvents = events.filter(event => 
    event.startTime <= now && 
    event.endTime > now && 
    event.status === 'live'
  )
  const myEvents = events.filter(event => 
    event.attendees.includes(currentUser.id) || event.organizer === currentUser.id
  )

  const filteredEvents = upcomingEvents.filter(event => 
    (searchQuery === '' || 
     event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     event.description.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (selectedType === null || event.type === selectedType)
  )

  const eventTypes = ['workshop', 'study_session', 'social', 'competition', 'presentation']

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'workshop': return <BookOpen className="w-5 h-5" />
      case 'study_session': return <Users className="w-5 h-5" />
      case 'social': return <Coffee className="w-5 h-5" />
      case 'competition': return <Trophy className="w-5 h-5" />
      case 'presentation': return <Presentation className="w-5 h-5" />
      default: return <Calendar className="w-5 h-5" />
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'workshop': return 'border-blue-400 text-blue-400'
      case 'study_session': return 'border-green-400 text-green-400'
      case 'social': return 'border-purple-400 text-purple-400'
      case 'competition': return 'border-yellow-400 text-yellow-400'
      case 'presentation': return 'border-red-400 text-red-400'
      default: return 'border-gray-400 text-gray-400'
    }
  }

  const formatEventTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getEventDuration = (startTime: Date, endTime: Date) => {
    const durationMs = endTime.getTime() - startTime.getTime()
    const hours = Math.floor(durationMs / (1000 * 60 * 60))
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const canJoinEvent = (event: SocialEvent) => {
    return !event.attendees.includes(currentUser.id) && 
           event.organizer !== currentUser.id &&
           (event.maxAttendees === undefined || event.attendees.length < event.maxAttendees)
  }

  const isOnWaitlist = (event: SocialEvent) => {
    return event.waitlist.includes(currentUser.id)
  }

  const renderEventCard = (event: SocialEvent, showActions: boolean = true) => {
    const isAttending = event.attendees.includes(currentUser.id)
    const isOrganizer = event.organizer === currentUser.id
    const isFull = event.maxAttendees && event.attendees.length >= event.maxAttendees
    const canJoin = canJoinEvent(event)
    const onWaitlist = isOnWaitlist(event)

    return (
      <motion.div
        key={event.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="group cursor-pointer"
        onClick={() => setSelectedEvent(event)}
      >
        <Card className="bg-gray-800 border-gray-700 hover:border-cyan-400 transition-all">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gray-700 rounded-lg">
                      {getEventIcon(event.type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-lg">{event.title}</h3>
                      <p className="text-sm text-gray-400">by Organizer</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-300 line-clamp-2">{event.description}</p>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge variant="outline" className={`text-xs capitalize ${getEventTypeColor(event.type)}`}>
                    {event.type.replace('_', ' ')}
                  </Badge>
                  {event.status === 'live' && (
                    <Badge className="bg-red-600 text-white text-xs animate-pulse">
                      LIVE
                    </Badge>
                  )}
                </div>
              </div>

              {/* Time and Location */}
              <div className="space-y-2">
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{formatEventTime(event.startTime)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{getEventDuration(event.startTime, event.endTime)}</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-400">
                  {event.location.type === 'virtual' ? (
                    <>
                      <Video className="w-4 h-4" />
                      <span>Virtual Event</span>
                      {event.location.platform && (
                        <span>• {event.location.platform}</span>
                      )}
                    </>
                  ) : (
                    <>
                      <MapPin className="w-4 h-4" />
                      <span>{event.location.address}</span>
                    </>
                  )}
                </div>
              </div>

              {/* Attendees */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex -space-x-2">
                    {event.attendees.slice(0, 4).map((attendeeId, index) => (
                      <Avatar key={attendeeId} className="w-8 h-8 border-2 border-gray-800">
                        <AvatarImage src={`/api/avatar/${attendeeId}`} />
                        <AvatarFallback className="text-xs">U{index + 1}</AvatarFallback>
                      </Avatar>
                    ))}
                    {event.attendees.length > 4 && (
                      <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-gray-800 flex items-center justify-center">
                        <span className="text-xs text-gray-300">+{event.attendees.length - 4}</span>
                      </div>
                    )}
                  </div>
                  <div className="text-sm text-gray-400">
                    <span>{event.attendees.length}</span>
                    {event.maxAttendees && <span>/{event.maxAttendees}</span>}
                    <span> attending</span>
                    {event.waitlist.length > 0 && (
                      <span> • {event.waitlist.length} waiting</span>
                    )}
                  </div>
                </div>

                {showActions && (
                  <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                    {isOrganizer ? (
                      <Button
                        size="sm"
                        onClick={() => onStartEvent(event.id)}
                        className="bg-green-600 hover:bg-green-700"
                        disabled={event.status === 'live'}
                      >
                        {event.status === 'live' ? (
                          <>
                            <Pause className="w-4 h-4 mr-1" />
                            Live
                          </>
                        ) : (
                          <>
                            <Play className="w-4 h-4 mr-1" />
                            Start
                          </>
                        )}
                      </Button>
                    ) : isAttending ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onLeaveEvent(event.id)}
                      >
                        <UserMinus className="w-4 h-4 mr-1" />
                        Leave
                      </Button>
                    ) : onWaitlist ? (
                      <Button size="sm" variant="outline" disabled>
                        On Waitlist
                      </Button>
                    ) : canJoin ? (
                      <Button
                        size="sm"
                        onClick={() => onJoinEvent(event.id)}
                        className="bg-cyan-600 hover:bg-cyan-700"
                      >
                        <UserPlus className="w-4 h-4 mr-1" />
                        Join
                      </Button>
                    ) : isFull ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onJoinWaitlist(event.id)}
                      >
                        Join Waitlist
                      </Button>
                    ) : null}
                  </div>
                )}
              </div>

              {/* Tags */}
              {event.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {event.tags.slice(0, 4).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {event.tags.length > 4 && (
                    <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                      +{event.tags.length - 4} more
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Social Events</h1>
          <p className="text-gray-400 mt-1">Join community events and activities</p>
        </div>
        <Button
          onClick={() => onCreateEvent({})}
          className="bg-cyan-600 hover:bg-cyan-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Event
        </Button>
      </div>

      {/* Live Events Banner */}
      {liveEvents.length > 0 && (
        <Card className="bg-gradient-to-r from-red-900/50 to-pink-900/50 border-red-600">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <div>
                  <h3 className="font-semibold text-white">Live Events</h3>
                  <p className="text-sm text-gray-300">{liveEvents.length} events happening now</p>
                </div>
              </div>
              <Button size="sm" className="bg-red-600 hover:bg-red-700">
                Join Live
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="upcoming" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-gray-800">
          <TabsTrigger value="upcoming">Upcoming ({upcomingEvents.length})</TabsTrigger>
          <TabsTrigger value="my-events">My Events ({myEvents.length})</TabsTrigger>
          <TabsTrigger value="live">Live ({liveEvents.length})</TabsTrigger>
          <TabsTrigger value="past">Past Events</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-6">
          {/* Search and Filters */}
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search events..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-700 border-gray-600"
                  />
                </div>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    variant={selectedType ? "outline" : "default"}
                    size="sm"
                    onClick={() => setSelectedType(null)}
                  >
                    All Types
                  </Button>
                  {eventTypes.map((type) => (
                    <Button
                      key={type}
                      variant={selectedType === type ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedType(selectedType === type ? null : type)}
                      className="capitalize"
                    >
                      {type.replace('_', ' ')}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Events Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredEvents.map(event => renderEventCard(event))}
          </div>

          {filteredEvents.length === 0 && (
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="p-8 text-center">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No events found</h3>
                <p className="text-gray-400">Try adjusting your search criteria or create a new event</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="my-events" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {myEvents.map(event => renderEventCard(event))}
          </div>
        </TabsContent>

        <TabsContent value="live" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {liveEvents.map(event => renderEventCard(event))}
          </div>
        </TabsContent>

        <TabsContent value="past" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {events.filter(event => event.status === 'completed').map(event => renderEventCard(event, false))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Event Detail Modal */}
      <AnimatePresence>
        {selectedEvent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-lg border border-gray-700 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-cyan-400">{selectedEvent.title}</h2>
                  <Button variant="outline" onClick={() => setSelectedEvent(null)}>
                    Close
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <p className="text-gray-300">{selectedEvent.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Start Time:</span>
                      <p className="text-white">{formatEventTime(selectedEvent.startTime)}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Duration:</span>
                      <p className="text-white">{getEventDuration(selectedEvent.startTime, selectedEvent.endTime)}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Location:</span>
                      <p className="text-white">
                        {selectedEvent.location.type === 'virtual' ? 'Virtual Event' : selectedEvent.location.address}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-400">Attendees:</span>
                      <p className="text-white">
                        {selectedEvent.attendees.length}
                        {selectedEvent.maxAttendees && `/${selectedEvent.maxAttendees}`}
                      </p>
                    </div>
                  </div>

                  {selectedEvent.requirements && selectedEvent.requirements.length > 0 && (
                    <div>
                      <h4 className="font-medium text-white mb-2">Requirements</h4>
                      <ul className="list-disc list-inside text-sm text-gray-300 space-y-1">
                        {selectedEvent.requirements.map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedEvent.resources.length > 0 && (
                    <div>
                      <h4 className="font-medium text-white mb-2">Resources</h4>
                      <div className="space-y-2">
                        {selectedEvent.resources.map((resource) => (
                          <div key={resource.id} className="flex items-center justify-between p-2 bg-gray-800 rounded">
                            <span className="text-sm text-gray-300">{resource.name}</span>
                            <Button size="sm" variant="outline">
                              <ExternalLink className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SocialEvents

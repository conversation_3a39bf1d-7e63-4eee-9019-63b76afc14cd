"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Avatar3DViewer } from './Avatar3DViewer'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { AvatarCustomization, Avatar3DSettings } from '@/types/avatar'
import { User, Maximize2, Minimize2, RotateCcw } from 'lucide-react'

interface AvatarShowcaseProps {
  customization: AvatarCustomization
  settings: Avatar3DSettings
  use3DAvatar: boolean
  fallbackAvatarUrl?: string
  username?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showControls?: boolean
  autoRotate?: boolean
  className?: string
  onClick?: () => void
}

const SIZE_CONFIGS = {
  sm: { width: 'w-12', height: 'h-12', avatar: 'w-12 h-12' },
  md: { width: 'w-16', height: 'h-16', avatar: 'w-16 h-16' },
  lg: { width: 'w-24', height: 'h-24', avatar: 'w-24 h-24' },
  xl: { width: 'w-32', height: 'h-32', avatar: 'w-32 h-32' }
}

export function AvatarShowcase({
  customization,
  settings,
  use3DAvatar,
  fallbackAvatarUrl = "/placeholder.svg",
  username = "User",
  size = 'md',
  showControls = false,
  autoRotate = false,
  className = "",
  onClick
}: AvatarShowcaseProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [currentAutoRotate, setCurrentAutoRotate] = useState(autoRotate)
  
  const sizeConfig = SIZE_CONFIGS[size]
  const initials = username.slice(0, 2).toUpperCase()

  const avatar3DSettings = {
    ...settings,
    autoRotate: currentAutoRotate,
    cameraDistance: size === 'sm' ? 2.5 : size === 'md' ? 2.2 : 2,
    quality: size === 'sm' ? 'low' as const : settings.quality
  }

  if (use3DAvatar) {
    return (
      <>
        <div 
          className={`relative ${sizeConfig.width} ${sizeConfig.height} rounded-full overflow-hidden border-2 border-cyan-500/50 cursor-pointer ${className}`}
          onClick={onClick || (showControls ? () => setIsExpanded(true) : undefined)}
        >
          <Avatar3DViewer
            customization={customization}
            settings={avatar3DSettings}
            className="w-full h-full"
          />
          
          {showControls && (
            <div className="absolute top-1 right-1">
              <Button
                size="sm"
                variant="ghost"
                className="w-6 h-6 p-0 bg-black/50 hover:bg-black/70"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsExpanded(true)
                }}
              >
                <Maximize2 className="w-3 h-3" />
              </Button>
            </div>
          )}
        </div>

        {/* Expanded Modal */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setIsExpanded(false)}
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="bg-gray-900 rounded-xl border border-gray-700 overflow-hidden max-w-2xl w-full"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-4 border-b border-gray-700 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <User className="w-5 h-5 text-cyan-400" />
                    <h3 className="text-lg font-semibold text-white">{username}&apos;s Avatar</h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setCurrentAutoRotate(!currentAutoRotate)}
                      className="border-gray-600"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsExpanded(false)}
                      className="border-gray-600"
                    >
                      <Minimize2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="aspect-square">
                  <Avatar3DViewer
                    customization={customization}
                    settings={{
                      ...settings,
                      autoRotate: currentAutoRotate,
                      quality: 'high'
                    }}
                    className="w-full h-full"
                  />
                </div>
                
                <div className="p-4 bg-gray-800/50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-300">3D Avatar</p>
                      <p className="text-xs text-gray-500">
                        Style: {customization.outfit} • Height: {Math.round(customization.height * 100)}%
                      </p>
                    </div>
                    <Badge variant="outline" className="border-cyan-500/50 text-cyan-400">
                      Interactive
                    </Badge>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    )
  }

  // Fallback to 2D avatar
  return (
    <Avatar 
      className={`${sizeConfig.avatar} border-2 border-cyan-500/50 cursor-pointer ${className}`}
      onClick={onClick}
    >
      <AvatarImage src={fallbackAvatarUrl} />
      <AvatarFallback className="bg-gradient-to-r from-cyan-500 to-blue-500">
        {initials}
      </AvatarFallback>
    </Avatar>
  )
}

// Compact avatar for lists and small spaces
export function AvatarCompact({
  customization,
  settings,
  use3DAvatar,
  fallbackAvatarUrl,
  username = "User",
  className = ""
}: Omit<AvatarShowcaseProps, 'size' | 'showControls'>) {
  return (
    <AvatarShowcase
      customization={customization}
      settings={settings}
      use3DAvatar={use3DAvatar}
      fallbackAvatarUrl={fallbackAvatarUrl}
      username={username}
      size="sm"
      showControls={false}
      className={className}
    />
  )
}

// Profile avatar with interactive features
export function AvatarProfile({
  customization,
  settings,
  use3DAvatar,
  fallbackAvatarUrl,
  username = "User",
  className = ""
}: Omit<AvatarShowcaseProps, 'size' | 'showControls' | 'autoRotate'>) {
  return (
    <AvatarShowcase
      customization={customization}
      settings={settings}
      use3DAvatar={use3DAvatar}
      fallbackAvatarUrl={fallbackAvatarUrl}
      username={username}
      size="xl"
      showControls={true}
      autoRotate={true}
      className={className}
    />
  )
}

// Dashboard widget avatar
export function AvatarDashboard({
  customization,
  settings,
  use3DAvatar,
  fallbackAvatarUrl,
  username = "User",
  level,
  xp,
  className = ""
}: Omit<AvatarShowcaseProps, 'size' | 'showControls'> & {
  level?: number
  xp?: number
}) {
  return (
    <Card className={`bg-gradient-to-r from-gray-800/50 to-gray-700/50 border-gray-700/50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <AvatarShowcase
            customization={customization}
            settings={settings}
            use3DAvatar={use3DAvatar}
            fallbackAvatarUrl={fallbackAvatarUrl}
            username={username}
            size="lg"
            showControls={false}
            autoRotate={use3DAvatar}
          />
          <div className="flex-1">
            <h3 className="font-semibold text-white">{username}</h3>
            {level && (
              <p className="text-sm text-gray-400">Level {level}</p>
            )}
            {xp && (
              <p className="text-xs text-cyan-400">{xp} XP</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  <PERSON>rkles, 
  Heart, 
  HelpCircle, 
  ThumbsUp, 
  MessageCircle, 
  PartyPopper,
  ChevronLeft,
  ChevronRight,
  Coins
} from 'lucide-react'
import { PresetPhrase } from '@/types/chat'
import { PHRASE_CATEGORIES, getPhrasesByCategory } from '@/data/presetPhrases'

interface PresetPhrasePickerProps {
  userLevel: number
  userAge: string
  userCubits: number
  onSelectPhrase: (phrase: PresetPhrase) => void
  onClose: () => void
  className?: string
}

const categoryIcons = {
  greeting: ThumbsUp,
  help: HelpCircle,
  thanks: Heart,
  encouragement: Sparkles,
  question: MessageCircle,
  celebration: PartyPopper
}

const categoryColors = {
  greeting: 'from-cyan-500 to-blue-500',
  help: 'from-green-500 to-emerald-500',
  thanks: 'from-purple-500 to-pink-500',
  encouragement: 'from-yellow-500 to-orange-500',
  question: 'from-blue-500 to-indigo-500',
  celebration: 'from-pink-500 to-rose-500'
}

export function PresetPhrasePicker({
  userLevel,
  userAge,
  userCubits,
  onSelectPhrase,
  onClose,
  className = ""
}: PresetPhrasePickerProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('greeting')
  const [currentPage, setCurrentPage] = useState(0)
  
  const availablePhrases = getPhrasesByCategory(selectedCategory, userLevel, userAge, userCubits)
  const phrasesPerPage = 6
  const totalPages = Math.ceil(availablePhrases.length / phrasesPerPage)
  const currentPhrases = availablePhrases.slice(
    currentPage * phrasesPerPage,
    (currentPage + 1) * phrasesPerPage
  )

  const handleSelectPhrase = (phrase: PresetPhrase) => {
    onSelectPhrase(phrase)
    onClose()
  }

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages)
  }

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`bg-black/95 backdrop-blur-xl border border-gray-800/50 rounded-xl p-6 max-w-2xl w-full ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white font-space-grotesk">
              Quantum Phrases
            </h3>
            <p className="text-sm text-gray-400">
              Choose a pre-written message to send
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
            <Coins className="w-3 h-3 mr-1" />
            {userCubits}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            ✕
          </Button>
        </div>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {PHRASE_CATEGORIES.map((category) => {
          const Icon = categoryIcons[category.id as keyof typeof categoryIcons]
          const isSelected = selectedCategory === category.id
          
          return (
            <motion.button
              key={category.id}
              onClick={() => {
                setSelectedCategory(category.id)
                setCurrentPage(0)
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
                isSelected
                  ? `bg-gradient-to-r ${categoryColors[category.id as keyof typeof categoryColors]} text-white shadow-lg`
                  : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700/50'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="text-sm">{category.name}</span>
              <span className="text-xs opacity-75">{category.icon}</span>
            </motion.button>
          )
        })}
      </div>

      {/* Phrases Grid */}
      <div className="min-h-[300px] mb-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={`${selectedCategory}-${currentPage}`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-3"
          >
            {currentPhrases.map((phrase, index) => {
              const canAfford = userCubits >= phrase.cubitsRequired
              const isUnlocked = userLevel >= phrase.unlockLevel
              
              return (
                <motion.button
                  key={phrase.id}
                  onClick={() => canAfford && isUnlocked && handleSelectPhrase(phrase)}
                  disabled={!canAfford || !isUnlocked}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={canAfford && isUnlocked ? { scale: 1.02 } : {}}
                  whileTap={canAfford && isUnlocked ? { scale: 0.98 } : {}}
                  className={`p-4 rounded-lg border text-left transition-all ${
                    canAfford && isUnlocked
                      ? 'bg-gray-800/50 border-gray-700 hover:bg-gray-700/50 hover:border-cyan-500/50 text-white'
                      : 'bg-gray-900/50 border-gray-800 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <span className="text-2xl">{phrase.emoji}</span>
                    <div className="flex flex-col items-end gap-1">
                      {phrase.cubitsRequired > 0 && (
                        <Badge 
                          variant={canAfford ? "secondary" : "destructive"}
                          className="text-xs"
                        >
                          <Coins className="w-3 h-3 mr-1" />
                          {phrase.cubitsRequired}
                        </Badge>
                      )}
                      {phrase.unlockLevel > 1 && (
                        <Badge 
                          variant={isUnlocked ? "default" : "outline"}
                          className="text-xs"
                        >
                          Lv.{phrase.unlockLevel}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <p className={`text-sm ${canAfford && isUnlocked ? 'text-gray-300' : 'text-gray-600'}`}>
                    {phrase.text}
                  </p>
                  {!isUnlocked && (
                    <p className="text-xs text-red-400 mt-2">
                      Unlock at level {phrase.unlockLevel}
                    </p>
                  )}
                  {!canAfford && isUnlocked && (
                    <p className="text-xs text-yellow-400 mt-2">
                      Need {phrase.cubitsRequired - userCubits} more Cubits
                    </p>
                  )}
                </motion.button>
              )
            })}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={prevPage}
            disabled={totalPages <= 1}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </Button>
          
          <div className="flex items-center gap-2">
            {Array.from({ length: totalPages }, (_, i) => (
              <button
                key={i}
                onClick={() => setCurrentPage(i)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  i === currentPage ? 'bg-cyan-400' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={nextPage}
            disabled={totalPages <= 1}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-4 p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
        <p className="text-cyan-400 text-xs">
          💡 <strong>Tip:</strong> Using preset phrases helps you communicate safely and earn Cubits! 
          Unlock more phrases by leveling up your communication skills.
        </p>
      </div>
    </motion.div>
  )
}

"use client"

import React, { useState, useCallback } from 'react'
import { 
  <PERSON>lette, 
  Zap, 
  Eye, 
  Sparkles, 
  RotateCcw,
  Save,
  Crown,
  Cpu
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  NanoCoreCustomization, 
  DEFAULT_NANOCORE_CUSTOMIZATION 
} from '@/types/avatar'

interface NanoCoreCustomizerProps {
  customization: NanoCoreCustomization
  onCustomizationChange: (updates: Partial<NanoCoreCustomization>) => void
  onSavePreset?: (name: string, description: string) => void
  className?: string
}

// Utility to ensure unique colors
const uniqueColors = (colors: string[]) => [...new Set(colors)]

// Color palettes for NanoCore
const CORE_COLORS = uniqueColors([
  '#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600', '#6600ff', '#ff0066'
])

const ENERGY_COLORS = uniqueColors([
  '#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600', '#6600ff', '#ff0066', '#ffffff'
])

const FRAME_COLORS = uniqueColors([
  '#2d3748', '#4a5568', '#718096', '#a0aec0', '#1a202c', '#2b6cb0', '#805ad5'
])

export function NanoCoreCustomizer({
  customization,
  onCustomizationChange,
  onSavePreset,
  className = ""
}: NanoCoreCustomizerProps) {
  const [activeTab, setActiveTab] = useState("core")
  
  const updateCustomization = useCallback((updates: Partial<NanoCoreCustomization>) => {
    onCustomizationChange(updates)
  }, [onCustomizationChange])
  
  const resetToDefault = () => {
    onCustomizationChange(DEFAULT_NANOCORE_CUSTOMIZATION)
  }
  
  const ColorPicker = ({ 
    colors, 
    currentColor, 
    onChange, 
    label 
  }: { 
    colors: string[]
    currentColor: string
    onChange: (color: string) => void
    label: string 
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-gray-300">{label}</Label>
      <div className="flex gap-2 flex-wrap">
        {colors.map((color) => (
          <button
            key={color}
            className={`w-8 h-8 rounded-full border-2 transition-all ${
              currentColor === color 
                ? 'border-cyan-400 scale-110' 
                : 'border-gray-600 hover:border-gray-400'
            }`}
            style={{ backgroundColor: color }}
            onClick={() => onChange(color)}
          />
        ))}
      </div>
    </div>
  )
  
  return (
    <Card className={`bg-gray-900/95 border-gray-700 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-bold text-cyan-400 flex items-center gap-2">
          <Cpu className="w-5 h-5" />
          NanoCore Customization
        </CardTitle>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={resetToDefault}
            className="border-gray-600 text-gray-300 hover:bg-gray-800"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset
          </Button>
          {onSavePreset && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSavePreset('Custom NanoCore', 'My custom NanoCore design')}
              className="border-cyan-600 text-cyan-400 hover:bg-cyan-900/20"
            >
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800">
            <TabsTrigger value="core" className="text-xs">
              <Zap className="w-4 h-4 mr-1" />
              Core
            </TabsTrigger>
            <TabsTrigger value="appearance" className="text-xs">
              <Palette className="w-4 h-4 mr-1" />
              Body
            </TabsTrigger>
            <TabsTrigger value="sensors" className="text-xs">
              <Eye className="w-4 h-4 mr-1" />
              Sensors
            </TabsTrigger>
            <TabsTrigger value="accessories" className="text-xs">
              <Crown className="w-4 h-4 mr-1" />
              Gear
            </TabsTrigger>
          </TabsList>

          {/* Core Tab */}
          <TabsContent value="core" className="space-y-4">
            {/* Core Type */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">Core Type</Label>
              <Select 
                value={customization.coreType} 
                onValueChange={(value) => updateCustomization({ coreType: value as any })}
              >
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="sphere">Sphere Core</SelectItem>
                  <SelectItem value="crystal">Crystal Core</SelectItem>
                  <SelectItem value="geometric">Geometric Core</SelectItem>
                  <SelectItem value="organic">Organic Core</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Core Color */}
            <ColorPicker
              colors={CORE_COLORS}
              currentColor={customization.coreColor}
              onChange={(color) => updateCustomization({ coreColor: color })}
              label="Core Color"
            />
            
            {/* Core Glow */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-300">
                Core Glow: {Math.round(customization.coreGlow * 100)}%
              </Label>
              <Slider
                value={[customization.coreGlow]}
                onValueChange={([value]) => updateCustomization({ coreGlow: value })}
                max={1}
                min={0}
                step={0.1}
                className="w-full"
              />
            </div>
            
            {/* Energy Core Settings */}
            <div className="space-y-3 border-t border-gray-700 pt-3">
              <Label className="text-sm font-medium text-cyan-400">Energy Core</Label>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">Type</Label>
                <Select 
                  value={customization.energyCore.type} 
                  onValueChange={(value) => updateCustomization({ 
                    energyCore: { ...customization.energyCore, type: value as any }
                  })}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="spark">Spark</SelectItem>
                    <SelectItem value="atom">Atom</SelectItem>
                    <SelectItem value="vortex">Vortex</SelectItem>
                    <SelectItem value="quantum">Quantum</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <ColorPicker
                colors={ENERGY_COLORS}
                currentColor={customization.energyCore.color}
                onChange={(color) => updateCustomization({ 
                  energyCore: { ...customization.energyCore, color }
                })}
                label="Energy Color"
              />
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">
                  Intensity: {Math.round(customization.energyCore.intensity * 100)}%
                </Label>
                <Slider
                  value={[customization.energyCore.intensity]}
                  onValueChange={([value]) => updateCustomization({ 
                    energyCore: { ...customization.energyCore, intensity: value }
                  })}
                  max={1}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  checked={customization.energyCore.particles}
                  onCheckedChange={(checked) => updateCustomization({ 
                    energyCore: { ...customization.energyCore, particles: checked }
                  })}
                />
                <Label className="text-sm text-gray-300">Particle Effects</Label>
              </div>
            </div>
          </TabsContent>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-4">
            {/* Head Unit */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-cyan-400">Head Unit</Label>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">Type</Label>
                <Select 
                  value={customization.headUnit.type} 
                  onValueChange={(value) => updateCustomization({ 
                    headUnit: { ...customization.headUnit, type: value as any }
                  })}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="quantum">Quantum</SelectItem>
                    <SelectItem value="neural">Neural</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">
                  Size: {Math.round(customization.headUnit.size * 100)}%
                </Label>
                <Slider
                  value={[customization.headUnit.size]}
                  onValueChange={([value]) => updateCustomization({ 
                    headUnit: { ...customization.headUnit, size: value }
                  })}
                  max={1.5}
                  min={0.8}
                  step={0.1}
                  className="w-full"
                />
              </div>
            </div>
            
            {/* Torso Frame */}
            <div className="space-y-3 border-t border-gray-700 pt-3">
              <Label className="text-sm font-medium text-cyan-400">Torso Frame</Label>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">Type</Label>
                <Select 
                  value={customization.torsoFrame.type} 
                  onValueChange={(value) => updateCustomization({ 
                    torsoFrame: { ...customization.torsoFrame, type: value as any }
                  })}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="exo">Exo-Frame</SelectItem>
                    <SelectItem value="membrane">Membrane</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                    <SelectItem value="energy">Energy Field</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <ColorPicker
                colors={FRAME_COLORS}
                currentColor={customization.torsoFrame.color}
                onChange={(color) => updateCustomization({ 
                  torsoFrame: { ...customization.torsoFrame, color }
                })}
                label="Frame Color"
              />
            </div>
            
            {/* Limbs */}
            <div className="space-y-3 border-t border-gray-700 pt-3">
              <Label className="text-sm font-medium text-cyan-400">Limbs</Label>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">Type</Label>
                <Select 
                  value={customization.limbs.type} 
                  onValueChange={(value) => updateCustomization({ 
                    limbs: { ...customization.limbs, type: value as any }
                  })}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="robotic">Robotic</SelectItem>
                    <SelectItem value="tentacle">Tentacle</SelectItem>
                    <SelectItem value="floating">Floating</SelectItem>
                    <SelectItem value="energy">Energy</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          {/* Sensors Tab */}
          <TabsContent value="sensors" className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm text-gray-300">Sensor Type</Label>
              <Select 
                value={customization.visualSensors.type} 
                onValueChange={(value) => updateCustomization({ 
                  visualSensors: { ...customization.visualSensors, type: value as any }
                })}
              >
                <SelectTrigger className="bg-gray-800 border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="mono">Mono Sensor</SelectItem>
                  <SelectItem value="dual">Dual Sensors</SelectItem>
                  <SelectItem value="scanner">Scanner Array</SelectItem>
                  <SelectItem value="compound">Compound Eyes</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <ColorPicker
              colors={ENERGY_COLORS}
              currentColor={customization.visualSensors.color}
              onChange={(color) => updateCustomization({ 
                visualSensors: { ...customization.visualSensors, color }
              })}
              label="Sensor Color"
            />
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={customization.visualSensors.glow}
                onCheckedChange={(checked) => updateCustomization({ 
                  visualSensors: { ...customization.visualSensors, glow: checked }
                })}
              />
              <Label className="text-sm text-gray-300">Glow Effect</Label>
            </div>
          </TabsContent>

          {/* Accessories Tab */}
          <TabsContent value="accessories" className="space-y-4">
            <div className="text-center text-gray-400 py-8">
              <Sparkles className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>Accessories unlock as you evolve!</p>
              <p className="text-sm">Current Stage: {customization.evolutionStage.stage}</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default NanoCoreCustomizer
